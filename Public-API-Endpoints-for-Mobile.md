# Public API Endpoints for Mobile Applications

This document outlines the public API endpoints that mobile applications should use instead of admin endpoints.

## Updated ENDPOINTS Configuration

Replace the admin endpoints in your mobile app's ENDPOINTS configuration with these public endpoints:

```javascript
const ENDPOINTS = {
  AUTH: {
    SEND_OTP: '/auth/send-otp',
    VERIFY_OTP: '/auth/verify-otp',
    COMPLETE_PROFILE: '/auth/complete-profile',
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout',
  },
  MUSIC: {
    SONGS: '/public/songs',                    // Changed from /admin/songs
    PLAYLISTS: '/playlist/generate',           // For AI playlist generation
    SEARCH: '/public/songs/search',            // Changed from /admin/songs/search
    TRENDING: '/music/trending',
    ALBUMS: '/public/albums',
    ARTISTS: '/public/artists',                // Changed from /admin/artist
    GET_SONGS_BY_ARTIST: '/public/songs/artist', // Changed from /admin/songs/artist
  },
  USER: {
    PROFILE: '/auth/profile',
    PREFERENCES: '/user/preferences',
  },
  ONBOARDING: {
    QUESTIONS: '/public/questions',
  },
  UPLOAD: '/upload',
  COUPONS: {
    LIST: '/public/coupons',                   // Changed from /admin/coupons
  }
}
```

## API Endpoint Mappings

### Songs API
| Old Admin Endpoint | New Public Endpoint | Description |
|-------------------|-------------------|-------------|
| `GET /api/v1/admin/songs` | `GET /api/v1/public/songs` | Get all active songs |
| `GET /api/v1/admin/songs/search?title={title}` | `GET /api/v1/public/songs/search?title={title}` | Search songs by title |
| `GET /api/v1/admin/songs/artist/{artistId}` | `GET /api/v1/public/songs/artist/{artistId}` | Get songs by artist |

### Artists API
| Old Admin Endpoint | New Public Endpoint | Description |
|-------------------|-------------------|-------------|
| `GET /api/v1/admin/artist` | `GET /api/v1/public/artists` | Get all active artists |
| `GET /api/v1/admin/artist/{id}` | `GET /api/v1/public/artists/{id}` | Get artist by ID |

### Coupons API
| Old Admin Endpoint | New Public Endpoint | Description |
|-------------------|-------------------|-------------|
| `GET /api/v1/admin/coupons` | `GET /api/v1/public/coupons` | Get active and valid coupons |
| `GET /api/v1/admin/coupons/{id}` | `GET /api/v1/public/coupons/{id}` | Get coupon by ID |
| `GET /api/v1/admin/coupons/code/{code}` | `GET /api/v1/public/coupons/code/{code}` | Get coupon by code |

## Additional Public Endpoints Available

### Songs
- `GET /api/v1/public/songs/featured` - Get featured songs
- `GET /api/v1/public/songs/popular` - Get popular songs
- `GET /api/v1/public/songs/top-rated` - Get top-rated songs
- `GET /api/v1/public/songs/genre/{genreId}` - Get songs by genre
- `GET /api/v1/public/songs/mood/{moodId}` - Get songs by mood
- `GET /api/v1/public/songs/language/{language}` - Get songs by language
- `POST /api/v1/public/songs/{id}/play` - Increment play count
- `POST /api/v1/public/songs/{id}/rate?rating={rating}` - Rate a song

### Albums
- `GET /api/v1/public/albums` - Get all active albums
- `GET /api/v1/public/albums/{id}` - Get album by ID
- `GET /api/v1/public/albums/{id}/songs` - Get songs in album
- `GET /api/v1/public/albums/artist/{artistId}` - Get albums by artist

### Genres
- `GET /api/v1/public/genres` - Get all active genres
- `GET /api/v1/public/genres/{id}` - Get genre by ID

### Moods
- `GET /api/v1/public/moods` - Get all active moods
- `GET /api/v1/public/moods/{id}` - Get mood by ID

### Coupons
- `GET /api/v1/public/coupons/type/{type}` - Get coupons by type (HOT_DEAL, EXCLUSIVE, FEATURED)

### Questions (Onboarding)
- `GET /api/v1/public/questions` - Get active onboarding questions
- `POST /api/v1/public/questions` - Submit user responses

### AI Features
- `GET /api/v1/public/queries/generate` - Generate AI music queries
- `POST /api/v1/playlist/generate` - Generate AI playlist from user query
- `POST /api/v1/playlist/fetch-metadata` - Extract metadata from user query

## Key Differences

1. **Data Filtering**: Public endpoints only return active/enabled items, while admin endpoints return all items
2. **No Authentication Required**: Public endpoints don't require admin authentication
3. **Read-Only**: Public endpoints are read-only (GET requests), no create/update/delete operations
4. **Optimized for Mobile**: Public endpoints are optimized for mobile consumption with appropriate data filtering

## Migration Steps

1. Update your mobile app's ENDPOINTS configuration
2. Test all API calls to ensure they work with the new endpoints
3. Remove any admin authentication headers from public API calls
4. Update error handling if needed (public endpoints may have different error responses)

## Notes

- All public endpoints return data in the same format as admin endpoints
- Public endpoints automatically filter out inactive/disabled items
- No breaking changes to response structure - only the endpoint URLs change
