# Album Management API Documentation for Frontend

## **Album Listing**

### **Admin Album List**
```
GET /api/v1/admin/albums
```
Returns: All albums (active + inactive)

### **Public Album List**
```
GET /api/v1/public/albums
```
Returns: Active albums only

## **Album Details**

### **Get Album Info**
```
GET /api/v1/public/albums/{albumId}
```
Returns: Album details with primary artist info

### **Get Album Songs**
```
GET /api/v1/public/albums/{albumId}/songs
```
Returns: Songs in album (ordered by track number)

## **Album Creation**

### **Step 1: Get Artists List**
```
GET /api/v1/admin/artist
```

### **Step 2: Get Available Songs**
```
GET /api/v1/admin/songs
```

### **Step 3: Create Album**
```
POST /api/v1/admin/albums
Content-Type: application/json

{
  "title": "Album Name",
  "description": "Description",
  "coverImageUrl": "https://...",
  "releaseDate": "2024-01-15",
  "primaryArtistId": 1,
  "songs": [
    {"songId": 10, "trackNumber": 1},
    {"songId": 15, "trackNumber": 2}
  ]
}
```

## **Album Editing**

### **Step 1: Get Album Details**
```
GET /api/v1/admin/albums/{albumId}
```

### **Step 2: Get Current Songs**
```
GET /api/v1/admin/albums/{albumId}/songs
```

### **Step 3: Get Available Songs to Add**
```
GET /api/v1/admin/albums/{albumId}/available-songs
```

### **Step 4: Update Album Info**
```
PUT /api/v1/admin/albums/{albumId}
Content-Type: application/json

{
  "title": "Updated Album Name",
  "description": "Updated description",
  "coverImageUrl": "https://...",
  "releaseDate": "2024-01-15",
  "primaryArtistId": 1
}
```

## **Song Management in Albums**

### **Add Song to Album**
```
POST /api/v1/admin/albums/{albumId}/songs/{songId}?trackNumber=3
```

### **Remove Song from Album**
```
DELETE /api/v1/admin/albums/{albumId}/songs/{songId}
```

## **Song Genre & Artist Management**

### **Update Song Genres**
```
PUT /api/v1/admin/songs/{songId}/genres
Content-Type: application/json

{
  "genreIds": [1, 2, 3]
}
```

### **Update Song Artists**
```
PUT /api/v1/admin/songs/{songId}/artists
Content-Type: application/json

{
  "artists": [
    {"artistId": 1, "role": "MAIN_ARTIST"},
    {"artistId": 2, "role": "FEATURED_ARTIST"}
  ]
}
```

## **Album Deletion**

### **Delete Album**
```
DELETE /api/v1/admin/albums/{albumId}
```
Note: This is soft delete (album becomes inactive)

## **Artist Albums**

### **Get Albums by Artist**
```
GET /api/v1/public/albums/artist/{artistId}
```

---

## **Quick Reference**

| Action | Method | Endpoint |
|--------|--------|----------|
| List all albums (admin) | GET | `/api/v1/admin/albums` |
| List active albums | GET | `/api/v1/public/albums` |
| Get album details | GET | `/api/v1/public/albums/{id}` |
| Get album songs | GET | `/api/v1/public/albums/{id}/songs` |
| Create album | POST | `/api/v1/admin/albums` |
| Update album | PUT | `/api/v1/admin/albums/{id}` |
| Delete album | DELETE | `/api/v1/admin/albums/{id}` |
| Get available songs for album | GET | `/api/v1/admin/albums/{id}/available-songs` |
| Add song to album | POST | `/api/v1/admin/albums/{albumId}/songs/{songId}?trackNumber=X` |
| Remove song from album | DELETE | `/api/v1/admin/albums/{albumId}/songs/{songId}` |
| Update song genres | PUT | `/api/v1/admin/songs/{id}/genres` |
| Update song artists | PUT | `/api/v1/admin/songs/{id}/artists` |
| Get artist albums | GET | `/api/v1/public/albums/artist/{artistId}` |

**Note**: All admin APIs require authentication. Public APIs are accessible without authentication.
