# AI-Driven Playlist Generation API

## Overview
This API generates music playlists based on natural language user queries using OpenAI for metadata extraction and database queries for song matching.

## Setup

### 1. Environment Variables
Set your OpenAI API key as an environment variable:
```bash
export OPENAI_API_KEY=your-openai-api-key-here
```

### 2. Configuration
The API uses the following configuration in `application.yml`:
```yaml
openai:
  api-key: ${OPENAI_API_KEY:your-openai-api-key-here}
  model: gpt-3.5-turbo
  max-tokens: 500
  temperature: 0.3
```

## API Endpoint

### Generate Playlist
**POST** `/api/v1/playlist/generate`

#### Request Body
```json
{
  "userQuery": "Play 15 English pop songs for workout like <PERSON><PERSON> style"
}
```

#### Response
```json
{
  "success": true,
  "data": {
    "playlist_name": "Workout Pop Hits",
    "tracks": [
      {
        "title": "Song Name",
        "artist": "Artist Name",
        "genre": ["pop"],
        "language": "English",
        "duration": 240,
        "fileUrl": "https://example.com/song.mp3"
      }
    ]
  }
}
```

## How It Works

### Step 1: Metadata Extraction
The system uses OpenAI to extract structured metadata from natural language queries:

**Input:** "Play 15 English pop songs for workout like Arijit Singh style"

**Extracted Metadata:**
```json
{
  "genre": "pop",
  "sub_genres": [],
  "language": "English",
  "artist": ["Arijit Singh"],
  "mood": null,
  "activity": "workout",
  "era": null,
  "count": 15,
  "duration": null,
  "tempo": null,
  "explicit": null,
  "popularity": null,
  "seed_tracks": [],
  "seed_albums": [],
  "region": null
}
```

### Step 2: Database Query
The system builds database queries based on extracted metadata:
- Filters by genre using `SongGenre` relationships
- Filters by artist using `SongArtist` relationships  
- Filters by language using direct song field
- Filters by mood using `SongMood` relationships
- Applies era filtering based on release dates
- Sorts by popularity if requested

### Step 3: Response Generation
Returns a structured playlist with song details in the specified JSON format.

## Supported Query Types

### Basic Examples
- "Play 10 Hindi songs"
- "Give me some English rock music"
- "Play Bollywood hits"

### Advanced Examples
- "Play 15 English pop songs for workout like Arijit Singh style"
- "Give me 20 romantic Hindi songs from the 90s"
- "Play trending Punjabi songs for party"
- "I want some sad English songs for study"

### Supported Metadata Fields
- **Genre**: pop, rock, classical, bollywood, etc.
- **Language**: English, Hindi, Punjabi, etc.
- **Artist**: Any artist name in the database
- **Mood**: happy, sad, romantic, energetic, etc.
- **Activity**: workout, study, party, relaxing, etc.
- **Era**: 90s, 2000s, 2010s, 2020s
- **Count**: Number of songs (default: 10)
- **Popularity**: trending, latest, top hits
- **Explicit**: true/false for explicit content

## Error Handling
- Invalid queries return empty playlists with error messages
- OpenAI failures fall back to default metadata (count: 10)
- Database errors return empty track lists
- All errors are logged for debugging

## Frontend Integration
Frontend teams can integrate by:
1. Sending POST requests to `/api/v1/playlist/generate`
2. Handling the structured JSON response
3. Playing songs using the provided `fileUrl` fields
4. Displaying playlist metadata like name and track count
