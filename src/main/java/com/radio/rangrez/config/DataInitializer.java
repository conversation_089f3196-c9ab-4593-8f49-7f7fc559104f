package com.radio.rangrez.config;

import com.radio.rangrez.enums.QuestionType;
import com.radio.rangrez.model.Genre;
import com.radio.rangrez.model.Mood;
import com.radio.rangrez.model.Question;
import com.radio.rangrez.model.QuestionOption;
import com.radio.rangrez.repository.GenreRepository;
import com.radio.rangrez.repository.MoodRepository;
import com.radio.rangrez.repository.QuestionRepository;
import com.radio.rangrez.repository.QuestionOptionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private QuestionRepository questionRepository;

    @Autowired
    private QuestionOptionRepository questionOptionRepository;

    @Autowired
    private GenreRepository genreRepository;

    @Autowired
    private MoodRepository moodRepository;

    @Override
    public void run(String... args) throws Exception {
        // Initialize genres and moods first
        if (genreRepository.count() == 0) {
            initializeGenres();
        }

        if (moodRepository.count() == 0) {
            initializeMoods();
        }

        // Only initialize if no questions exist
        if (questionRepository.count() == 0) {
            initializeQuestions();
        }
    }

    private void initializeQuestions() {
        // Question 1: What do you usually listen to?
        Question q1 = createQuestion(
            "What do you usually listen to?",
            "Choose all that apply",
            QuestionType.MULTI_SELECT,
            1
        );
        q1 = questionRepository.save(q1);
        
        createOptions(q1, Arrays.asList(
            new OptionData("Talk shows", "talk_shows", 1),
            new OptionData("Live radio", "live_radio", 2),
            new OptionData("News & Updates", "news_updates", 3),
            new OptionData("Podcasts", "podcasts", 4),
            new OptionData("Music", "music", 5),
            new OptionData("Interviews & Debates", "interviews_debates", 6)
        ));

        // Question 2: What's your favorite type of music?
        Question q2 = createQuestion(
            "What's your favorite type of music?",
            "Select your preferred music genres",
            QuestionType.MULTI_SELECT,
            2
        );
        q2 = questionRepository.save(q2);
        
        createOptions(q2, Arrays.asList(
            new OptionData("Rock", "rock", 1),
            new OptionData("Pop", "pop", 2),
            new OptionData("Jazz", "jazz", 3),
            new OptionData("Folk", "folk", 4),
            new OptionData("Classical", "classical", 5),
            new OptionData("Hip-hop / Rap", "hiphop_rap", 6),
            new OptionData("Bollywood", "bollywood", 7),
            new OptionData("Devotional / Spiritual", "devotional_spiritual", 8),
            new OptionData("Instrumental", "instrumental", 9)
        ));

        // Question 3: What languages do you prefer to listen in?
        Question q3 = createQuestion(
            "What languages do you prefer to listen in?",
            "Select your preferred languages",
            QuestionType.MULTI_SELECT,
            3
        );
        q3 = questionRepository.save(q3);
        
        createOptions(q3, Arrays.asList(
            new OptionData("English", "english", 1),
            new OptionData("Hindi", "hindi", 2),
            new OptionData("Gujarati", "gujarati", 3),
            new OptionData("Tamil", "tamil", 4),
            new OptionData("Telugu", "telugu", 5),
            new OptionData("Marathi", "marathi", 6),
            new OptionData("Bengali", "bengali", 7),
            new OptionData("Punjabi", "punjabi", 8),
            new OptionData("Kannada", "kannada", 9),
            new OptionData("Malayalam", "malayalam", 10)
        ));

        // Question 4: When do you mostly listen?
        Question q4 = createQuestion(
            "When do you mostly listen?",
            "Select your preferred listening times",
            QuestionType.MULTI_SELECT,
            4
        );
        q4 = questionRepository.save(q4);
        
        createOptions(q4, Arrays.asList(
            new OptionData("Morning (6AM-9AM)", "morning", 1),
            new OptionData("Daytime (9AM-3PM)", "daytime", 2),
            new OptionData("Evening (3PM-6PM)", "evening", 3),
            new OptionData("Night (9PM-12AM)", "night", 4),
            new OptionData("Late Night (12AM-6AM)", "late_night", 5),
            new OptionData("No fixed time", "no_fixed_time", 6)
        ));

        // Question 5: What's your mood right now?
        Question q5 = createQuestion(
            "What's your mood right now?",
            "Choose your current mood",
                QuestionType.SINGLE_SELECT,
            5
        );
        q5 = questionRepository.save(q5);
        
        createOptions(q5, Arrays.asList(
            new OptionData("Happy", "happy", 1),
            new OptionData("Sad", "sad", 2),
            new OptionData("Relaxing", "relaxing", 3),
            new OptionData("Energetic", "energetic", 4),
            new OptionData("Romantic", "romantic", 5),
            new OptionData("Calm", "calm", 6),
            new OptionData("Workout", "workout", 7)
        ));
    }

    private Question createQuestion(String text, String description, QuestionType type, int order) {
        Question question = new Question();
        question.setQuestionText(text);
        question.setDescription(description);
        question.setQuestionType(type);
        question.setDisplayOrder(order);
        question.setIsRequired(true);
        question.setActivated(true);
        return question;
    }

    private void createOptions(Question question, List<OptionData> optionsData) {
        List<QuestionOption> options = optionsData.stream()
            .map(data -> {
                QuestionOption option = new QuestionOption();
                option.setOptionText(data.text);
                option.setOptionValue(data.value);
                option.setDisplayOrder(data.order);
                option.setQuestion(question);
                option.setActivated(true);
                return option;
            })
            .toList();
        
        questionOptionRepository.saveAll(options);
    }

    private static class OptionData {
        String text;
        String value;
        int order;

        OptionData(String text, String value, int order) {
            this.text = text;
            this.value = value;
            this.order = order;
        }
    }

    private void initializeGenres() {
        List<Genre> genres = Arrays.asList(
            createGenre("Rock", "Rock music with electric guitars and strong rhythms", "#E74C3C", 1),
            createGenre("Pop", "Popular mainstream music", "#3498DB", 2),
            createGenre("Jazz", "Improvisational music with complex harmonies", "#F39C12", 3),
            createGenre("Classical", "Traditional orchestral and chamber music", "#9B59B6", 4),
            createGenre("Hip-Hop", "Rhythmic spoken lyrics over beats", "#2ECC71", 5),
            createGenre("Electronic", "Synthesized and computer-generated music", "#1ABC9C", 6),
            createGenre("Folk", "Traditional acoustic music", "#E67E22", 7),
            createGenre("Bollywood", "Indian film music", "#E91E63", 8),
            createGenre("Country", "American rural and western music", "#8E44AD", 9),
            createGenre("R&B", "Rhythm and blues music", "#34495E", 10),
            createGenre("Reggae", "Jamaican music with distinctive rhythm", "#27AE60", 11),
            createGenre("Blues", "Soulful music expressing emotions", "#2980B9", 12)
        );

        genreRepository.saveAll(genres);
    }

    private void initializeMoods() {
        List<Mood> moods = Arrays.asList(
            createMood("Happy", "Uplifting and joyful music", "#FFD700", 1),
            createMood("Sad", "Melancholic and emotional music", "#4682B4", 2),
            createMood("Energetic", "High energy and motivating music", "#FF4500", 3),
            createMood("Relaxing", "Calm and peaceful music", "#32CD32", 4),
            createMood("Romantic", "Love and romance themed music", "#FF69B4", 5),
            createMood("Workout", "Perfect for exercise and fitness", "#DC143C", 6),
            createMood("Focus", "Music for concentration and productivity", "#4169E1", 7),
            createMood("Party", "Upbeat music for celebrations", "#FF6347", 8),
            createMood("Chill", "Laid-back and mellow vibes", "#20B2AA", 9),
            createMood("Motivational", "Inspiring and empowering music", "#FF8C00", 10)
        );

        moodRepository.saveAll(moods);
    }

    private Genre createGenre(String name, String description, String colorCode, int displayOrder) {
        Genre genre = new Genre();
        genre.setName(name);
        genre.setDescription(description);
        genre.setColorCode(colorCode);
        genre.setDisplayOrder(displayOrder);
        genre.setActivated(true);
        return genre;
    }

    private Mood createMood(String name, String description, String colorCode, int displayOrder) {
        Mood mood = new Mood();
        mood.setName(name);
        mood.setDescription(description);
        mood.setColorCode(colorCode);
        mood.setDisplayOrder(displayOrder);
        mood.setActivated(true);
        return mood;
    }
}
