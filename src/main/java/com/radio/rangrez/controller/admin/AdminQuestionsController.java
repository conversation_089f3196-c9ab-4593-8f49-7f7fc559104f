package com.radio.rangrez.controller.admin;

import com.radio.rangrez.model.response.RestResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/api/v1/admin")
@RestController
public class AdminQuestionsController {

    @PostMapping("/create")
    private RestResponse createQuestions(){
        return new RestResponse(true, "Question Created successfully");
    }

}
