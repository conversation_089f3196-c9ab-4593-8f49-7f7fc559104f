package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.AlbumDto;
import com.radio.rangrez.dto.CreateAlbumRequest;
import com.radio.rangrez.dto.SongDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.album.AlbumService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/albums")
@RestController
public class AlbumController {

    @Autowired
    private AlbumService albumService;

    @PostMapping
    public RestResponse createAlbum(@Valid @RequestBody CreateAlbumRequest request) {
            AlbumDto createdAlbum = albumService.createAlbum(request);
            return new RestResponse(true, createdAlbum);
    }

    @GetMapping
    public RestResponse getAllAlbums() {
        List<AlbumDto> albums = albumService.getAllAlbums();
        return new RestResponse(true, albums);
    }

    @GetMapping("/{id}")
    public RestResponse getAlbumById(@PathVariable Long id) {
        AlbumDto album = albumService.getAlbumById(id);
        return new RestResponse(true, album);
    }

    @PutMapping("/{id}")
    public RestResponse updateAlbum(@PathVariable Long id, @Valid @RequestBody CreateAlbumRequest request) {
        AlbumDto updatedAlbum = albumService.updateAlbum(id, request);
        return new RestResponse(true, updatedAlbum);
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteAlbum(@PathVariable Long id) {
        boolean deleted = albumService.deleteAlbum(id);
        return new RestResponse(deleted, deleted ? "Album deleted successfully" : "Failed to delete album");
    }

    @GetMapping("/{id}/songs")
    public RestResponse getAlbumSongs(@PathVariable Long id) {
        List<SongDto> songs = albumService.getAlbumSongs(id);
        return new RestResponse(true, songs);
    }

    @PostMapping("/{albumId}/songs/{songId}")
    public RestResponse addSongToAlbum(@PathVariable Long albumId, @PathVariable Long songId, @RequestParam Integer trackNumber) {
        AlbumDto updatedAlbum = albumService.addSongToAlbum(albumId, songId, trackNumber);
        return new RestResponse(true, updatedAlbum);
    }

    @DeleteMapping("/{albumId}/songs/{songId}")
    public RestResponse removeSongFromAlbum(@PathVariable Long albumId, @PathVariable Long songId) {
        AlbumDto updatedAlbum = albumService.removeSongFromAlbum(albumId, songId);
        return new RestResponse(true, updatedAlbum);
    }

    @GetMapping("/artist/{artistId}")
    public RestResponse getAlbumsByArtist(@PathVariable Long artistId) {
        List<AlbumDto> albums = albumService.getAlbumsByArtist(artistId);
        return new RestResponse(true, albums);
    }

    @GetMapping("/{albumId}/available-songs")
    public RestResponse getAvailableSongsForAlbum(@PathVariable Long albumId) {
        List<SongDto> availableSongs = albumService.getAvailableSongsForAlbum(albumId);
        return new RestResponse(true, availableSongs);
    }
}
