package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.ArtistDto;
import com.radio.rangrez.dto.ArtistGenreRequest;
import com.radio.rangrez.dto.GenreDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.artist.ArtistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/artist")
@RestController
public class ArtistController {

    @Autowired
    private ArtistService artistService;

    @PostMapping
    public RestResponse createArtist(@RequestBody ArtistDto artistDto){
        ArtistDto createdArtist = artistService.createArtist(artistDto);
        return new RestResponse(true, createdArtist);
    }

    @GetMapping
    public RestResponse getAllArtists(){
        List<ArtistDto> artists = artistService.getAllArtists();
        return new RestResponse(true, artists);
    }

    @GetMapping("/{id}")
    public RestResponse getArtistById(@PathVariable Long id){
        ArtistDto artist = artistService.getArtistById(id);
        return new RestResponse(true, artist);
    }

    @PutMapping("/{id}")
    public RestResponse updateArtist(@PathVariable Long id, @RequestBody ArtistDto artistDto){
        ArtistDto updatedArtist = artistService.updateArtist(id, artistDto);
        return new RestResponse(true, updatedArtist);
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteArtist(@PathVariable Long id){
        boolean deleted = artistService.deleteArtist(id);
        return new RestResponse(deleted, deleted ? "Artist deleted successfully" : "Failed to delete artist");
    }

    // Genre management endpoints
    @GetMapping("/{id}/genres")
    public RestResponse getArtistGenres(@PathVariable Long id) {
        List<GenreDto> genres = artistService.getArtistGenres(id);
        return new RestResponse(true, genres);
    }

    @PostMapping("/{id}/genres")
    public RestResponse addGenresToArtist(@PathVariable Long id, @RequestBody ArtistGenreRequest request) {
        ArtistDto updatedArtist = artistService.addGenresToArtist(id, request);
        return new RestResponse(true, updatedArtist);
    }

    @PutMapping("/{id}/genres")
    public RestResponse updateArtistGenres(@PathVariable Long id, @RequestBody ArtistGenreRequest request) {
        ArtistDto updatedArtist = artistService.updateArtistGenres(id, request);
        return new RestResponse(true, updatedArtist);
    }

    @DeleteMapping("/{id}/genres/{genreId}")
    public RestResponse removeGenreFromArtist(@PathVariable Long id, @PathVariable Long genreId) {
        ArtistDto updatedArtist = artistService.removeGenreFromArtist(id, genreId);
        return new RestResponse(true, updatedArtist);
    }

}
