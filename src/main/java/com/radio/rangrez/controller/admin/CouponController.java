package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.CreateCouponRequest;
import com.radio.rangrez.dto.CouponDto;
import com.radio.rangrez.dto.ReorderCouponsRequest;
import com.radio.rangrez.dto.UpdateCouponPriorityRequest;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.coupon.CouponService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/coupons")
@RestController
public class CouponController {

    @Autowired
    private CouponService couponService;

    @PostMapping
    public RestResponse createCoupon(@Valid @RequestBody CreateCouponRequest request) {
            CouponDto createdCoupon = couponService.createCoupon(request);
            return new RestResponse(true, createdCoupon);
    }

    @GetMapping
    public RestResponse getAllCoupons() {
        List<CouponDto> coupons = couponService.getAllCoupons();
        return new RestResponse(true, coupons);
    }

    @GetMapping("/active")
    public RestResponse getActiveCoupons() {
        List<CouponDto> coupons = couponService.getActiveCoupons();
        return new RestResponse(true, coupons);
    }

    @GetMapping("/valid")
    public RestResponse getActiveAndValidCoupons() {
        List<CouponDto> coupons = couponService.getActiveAndValidCoupons();
        return new RestResponse(true, coupons);
    }

    @GetMapping("/expired")
    public RestResponse getExpiredCoupons() {
        List<CouponDto> coupons = couponService.getExpiredCoupons();
        return new RestResponse(true, coupons);
    }

    @GetMapping("/code/{couponCode}")
    public RestResponse getCouponByCode(@PathVariable String couponCode) {
        CouponDto coupon = couponService.getCouponByCode(couponCode);
        return new RestResponse(true, coupon);
    }

    @PostMapping("/reorder")
    public RestResponse reorderCoupons(@Valid @RequestBody ReorderCouponsRequest request) {
        couponService.reorderCoupons(request.getCoupons());
        return new RestResponse(true, "Coupons reordered successfully");
    }

    @GetMapping("/{id}")
    public RestResponse getCouponById(@PathVariable Long id) {
        CouponDto coupon = couponService.getCouponById(id);
        return new RestResponse(true, coupon);
    }

    @PutMapping("/{id}")
    public RestResponse updateCoupon(@PathVariable Long id, @Valid @RequestBody CreateCouponRequest request) {
            return new RestResponse(true, couponService.updateCoupon(id, request));
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteCoupon(@PathVariable Long id) {
        boolean deleted = couponService.deleteCoupon(id);
        return new RestResponse(deleted, deleted ? "Coupon deleted successfully" : "Failed to delete coupon");
    }

    @PostMapping("/expire")
    public RestResponse expireAndDeactivateCoupons() {
        int deactivatedCount = couponService.expireAndDeactivateCoupons();
        String message = deactivatedCount > 0
            ? String.format("Successfully deactivated %d expired coupons", deactivatedCount)
            : "No expired coupons found to deactivate";
        return new RestResponse(true, message);
    }
}
