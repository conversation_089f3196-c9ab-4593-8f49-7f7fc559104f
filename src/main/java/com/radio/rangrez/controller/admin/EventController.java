package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.event.CreateEventRequest;
import com.radio.rangrez.dto.event.EventDto;
import com.radio.rangrez.dto.event.EventResponse;
import com.radio.rangrez.dto.event.UpdateEventRequest;
import com.radio.rangrez.model.User;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.event.EventService;
import com.radio.rangrez.service.user.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RequestMapping("/api/v1/admin/events")
@RestController
public class EventController {

    @Autowired
    private EventService eventService;

    @Autowired
    private UserService userService;

    @PostMapping
    public RestResponse createEvent(@Valid @RequestBody CreateEventRequest request) {
        User currentUser = userService.getCurrentUser();
        EventResponse eventResponse = eventService.createEvent(request, currentUser.getEmail());
        return new RestResponse(true, eventResponse);
    }

    @GetMapping
    public RestResponse getAllEvents() {
        List<EventDto> events = eventService.getAllEvents();
        return new RestResponse(true, events);
    }

    @GetMapping("/{id}")
    public RestResponse getEventById(@PathVariable Long id) {
        EventDto event = eventService.getEventById(id);
        return new RestResponse(true, event);
    }

    @PutMapping("/{id}")
    public RestResponse updateEvent(@PathVariable Long id, @Valid @RequestBody UpdateEventRequest request) {
        User currentUser = userService.getCurrentUser();
        EventResponse eventResponse = eventService.updateEvent(id, request, currentUser.getEmail());
        return new RestResponse(true, eventResponse);
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteEvent(@PathVariable Long id) {
        User currentUser = userService.getCurrentUser();
        boolean deleted = eventService.deleteEvent(id, currentUser.getEmail());
        return new RestResponse(deleted, deleted ? "Event deleted successfully" : "Failed to delete event");
    }

    @GetMapping("/my-events")
    public RestResponse getMyEvents() {
        User currentUser = userService.getCurrentUser();
        List<EventDto> events = eventService.getEventsByUser(currentUser.getEmail());
        return new RestResponse(true, events);
    }

    @GetMapping("/upcoming")
    public RestResponse getUpcomingEvents() {
        List<EventDto> events = eventService.getUpcomingEvents();
        return new RestResponse(true, events);
    }

    @GetMapping("/past")
    public RestResponse getPastEvents() {
        List<EventDto> events = eventService.getPastEvents();
        return new RestResponse(true, events);
    }

    @GetMapping("/between")
    public RestResponse getEventsBetweenDates(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        List<EventDto> events = eventService.getEventsBetweenDates(startDate, endDate);
        return new RestResponse(true, events);
    }

    @PostMapping("/{id}/sync-google-calendar")
    public RestResponse syncWithGoogleCalendar(@PathVariable Long id) {
        User currentUser = userService.getCurrentUser();
        EventResponse eventResponse = eventService.syncEventWithGoogleCalendar(id, currentUser.getEmail());
        return new RestResponse(true, eventResponse);
    }
}
