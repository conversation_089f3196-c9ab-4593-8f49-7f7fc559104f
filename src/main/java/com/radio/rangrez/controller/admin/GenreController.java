package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.CreateGenreRequest;
import com.radio.rangrez.dto.GenreDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.genre.GenreService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/genres")
@RestController
public class GenreController {

    @Autowired
    private GenreService genreService;

    @PostMapping
    public RestResponse createGenre(@Valid @RequestBody CreateGenreRequest request) {
        GenreDto createdGenre = genreService.createGenre(request);
        return new RestResponse(true, createdGenre);
    }

    @GetMapping
    public RestResponse getAllGenres() {
        List<GenreDto> genres = genreService.getAllGenres();
        return new RestResponse(true, genres);
    }

    @GetMapping("/active")
    public RestResponse getActiveGenres() {
        List<GenreDto> genres = genreService.getActiveGenres();
        return new RestResponse(true, genres);
    }

    @GetMapping("/{id}")
    public RestResponse getGenreById(@PathVariable Long id) {
        GenreDto genre = genreService.getGenreById(id);
        return new RestResponse(true, genre);
    }

    @PutMapping("/{id}")
    public RestResponse updateGenre(@PathVariable Long id, @Valid @RequestBody CreateGenreRequest request) {
        GenreDto updatedGenre = genreService.updateGenre(id, request);
        return new RestResponse(true, updatedGenre);
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteGenre(@PathVariable Long id) {
        boolean deleted = genreService.deleteGenre(id);
        return new RestResponse(deleted, deleted ? "Genre deleted successfully" : "Failed to delete genre");
    }
}
