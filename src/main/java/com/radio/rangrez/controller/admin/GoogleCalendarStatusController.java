package com.radio.rangrez.controller.admin;

import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.event.GoogleCalendarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RequestMapping("/api/v1/admin/google-calendar")
@RestController
public class GoogleCalendarStatusController {

    @Autowired
    private GoogleCalendarService googleCalendarService;

    @Value("${google.calendar.enabled:false}")
    private boolean googleCalendarEnabled;

    @GetMapping("/status")
    public RestResponse getGoogleCalendarStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("enabled", googleCalendarEnabled);
        status.put("configured", googleCalendarService.isGoogleCalendarEnabled());

        if (!googleCalendarEnabled) {
            status.put("message", "Google Calendar integration is disabled. Set 'google.calendar.enabled=true' in application.yml to enable it.");
        } else if (!googleCalendarService.isGoogleCalendarEnabled()) {
            status.put("message", "Google Calendar is enabled but not properly configured. Please check your credentials.json file.");
        } else {
            status.put("message", "Google Calendar integration is enabled and configured.");
        }

        return new RestResponse(true, status);
    }
}
