package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.CreateMoodRequest;
import com.radio.rangrez.dto.MoodDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.mood.MoodService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/moods")
@RestController
public class MoodController {

    @Autowired
    private MoodService moodService;

    @PostMapping
    public RestResponse createMood(@Valid @RequestBody CreateMoodRequest request) {
        MoodDto createdMood = moodService.createMood(request);
        return new RestResponse(true, createdMood);
    }

    @GetMapping
    public RestResponse getAllMoods() {
        List<MoodDto> moods = moodService.getAllMoods();
        return new RestResponse(true, moods);
    }

    @GetMapping("/active")
    public RestResponse getActiveMoods() {
        List<MoodDto> moods = moodService.getActiveMoods();
        return new RestResponse(true, moods);
    }

    @GetMapping("/{id}")
    public RestResponse getMoodById(@PathVariable Long id) {
        MoodDto mood = moodService.getMoodById(id);
        return new RestResponse(true, mood);
    }

    @PutMapping("/{id}")
    public RestResponse updateMood(@PathVariable Long id, @Valid @RequestBody CreateMoodRequest request) {
        MoodDto updatedMood = moodService.updateMood(id, request);
        return new RestResponse(true, updatedMood);
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteMood(@PathVariable Long id) {
        boolean deleted = moodService.deleteMood(id);
        return new RestResponse(deleted, deleted ? "Mood deleted successfully" : "Failed to delete mood");
    }
}
