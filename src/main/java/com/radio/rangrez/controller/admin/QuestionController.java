package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.CreateQuestionRequest;
import com.radio.rangrez.dto.QuestionDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.question.QuestionService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/questions")
@RestController
public class QuestionController {

    @Autowired
    private QuestionService questionService;

    @PostMapping
    public RestResponse createQuestion(@Valid @RequestBody CreateQuestionRequest request) {
        QuestionDto createdQuestion = questionService.createQuestion(request);
        return new RestResponse(true, createdQuestion);
    }

    @GetMapping
    public RestResponse getAllQuestions() {
        List<QuestionDto> questions = questionService.getAllQuestions();
        return new RestResponse(true, questions);
    }

    @GetMapping("/active")
    public RestResponse getActiveQuestions() {
        List<QuestionDto> questions = questionService.getActiveQuestions();
        return new RestResponse(true, questions);
    }

    @GetMapping("/{id}")
    public RestResponse getQuestionById(@PathVariable Long id) {
        QuestionDto question = questionService.getQuestionById(id);
        return new RestResponse(true, question);
    }

    @PutMapping("/{id}")
    public RestResponse updateQuestion(@PathVariable Long id, @Valid @RequestBody CreateQuestionRequest request) {
        QuestionDto updatedQuestion = questionService.updateQuestion(id, request);
        return new RestResponse(true, updatedQuestion);
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteQuestion(@PathVariable Long id) {
        boolean deleted = questionService.deleteQuestion(id);
        return new RestResponse(deleted, deleted ? "Question deleted successfully" : "Failed to delete question");
    }


}
