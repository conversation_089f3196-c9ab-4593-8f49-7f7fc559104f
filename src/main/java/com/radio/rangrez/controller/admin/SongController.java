package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.CreateSongRequest;
import com.radio.rangrez.dto.SongDto;
import com.radio.rangrez.dto.UpdateSongArtistsRequest;
import com.radio.rangrez.dto.UpdateSongGenresRequest;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.song.SongService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/songs")
@RestController
public class SongController {

    @Autowired
    private SongService songService;

    @PostMapping
    public RestResponse createSong(@Valid @RequestBody CreateSongRequest request) {
        SongDto createdSong = songService.createSong(request);
        return new RestResponse(true, createdSong);
    }

    @GetMapping
    public RestResponse getAllSongs() {
        List<SongDto> songs = songService.getAllSongs();
        return new RestResponse(true, songs);
    }

    @GetMapping("/paginated")
    public RestResponse getAllSongs(Pageable pageable) {
        Page<SongDto> songs = songService.getAllSongs(pageable);
        return new RestResponse(true, songs);
    }

    @GetMapping("/active")
    public RestResponse getActiveSongs() {
        List<SongDto> songs = songService.getActiveSongs();
        return new RestResponse(true, songs);
    }

    @GetMapping("/{id}")
    public RestResponse getSongById(@PathVariable Long id) {
        SongDto song = songService.getSongById(id);
        return new RestResponse(true, song);
    }

    @PutMapping("/{id}")
    public RestResponse updateSong(@PathVariable Long id, @Valid @RequestBody CreateSongRequest request) {
        SongDto updatedSong = songService.updateSong(id, request);
        return new RestResponse(true, updatedSong);
    }

    @PutMapping("/{id}/genres")
    public RestResponse updateSongGenres(@PathVariable Long id, @Valid @RequestBody UpdateSongGenresRequest request) {
        SongDto updatedSong = songService.updateSongGenres(id, request);
        return new RestResponse(true, updatedSong);
    }

    @PutMapping("/{id}/artists")
    public RestResponse updateSongArtists(@PathVariable Long id, @Valid @RequestBody UpdateSongArtistsRequest request) {
        SongDto updatedSong = songService.updateSongArtists(id, request);
        return new RestResponse(true, updatedSong);
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteSong(@PathVariable Long id) {
        boolean deleted = songService.deleteSong(id);
        return new RestResponse(deleted, deleted ? "Song deleted successfully" : "Failed to delete song");
    }

    @GetMapping("/search")
    public RestResponse searchSongs(@RequestParam String title) {
        List<SongDto> songs = songService.searchSongsByTitle(title);
        return new RestResponse(true, songs);
    }

    @GetMapping("/album/{albumName}")
    public RestResponse getSongsByAlbum(@PathVariable String albumName) {
        List<SongDto> songs = songService.getSongsByAlbum(albumName);
        return new RestResponse(true, songs);
    }

    @GetMapping("/language/{language}")
    public RestResponse getSongsByLanguage(@PathVariable String language) {
        List<SongDto> songs = songService.getSongsByLanguage(language);
        return new RestResponse(true, songs);
    }

    @GetMapping("/artist/{artistId}")
    public RestResponse getSongsByArtist(@PathVariable Long artistId) {
        List<SongDto> songs = songService.getSongsByArtist(artistId);
        return new RestResponse(true, songs);
    }

    @GetMapping("/genre/{genreId}")
    public RestResponse getSongsByGenre(@PathVariable Long genreId) {
        List<SongDto> songs = songService.getSongsByGenre(genreId);
        return new RestResponse(true, songs);
    }

    @GetMapping("/mood/{moodId}")
    public RestResponse getSongsByMood(@PathVariable Long moodId) {
        List<SongDto> songs = songService.getSongsByMood(moodId);
        return new RestResponse(true, songs);
    }

    @PostMapping("/{id}/rate")
    public RestResponse rateSong(@PathVariable Long id, @RequestParam Double rating) {
        SongDto updatedSong = songService.rateSong(id, rating);
        return new RestResponse(true, updatedSong);
    }
}
