package com.radio.rangrez.controller.coupon;

import com.radio.rangrez.dto.CouponDto;
import com.radio.rangrez.model.Coupon;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.coupon.CouponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/public/coupons")
@RestController
public class PublicCouponController {

    @Autowired
    private CouponService couponService;

    @GetMapping
    public RestResponse getActiveAndValidCoupons() {
        List<CouponDto> coupons = couponService.getActiveAndValidCoupons();
        return new RestResponse(true, coupons);
    }

    @GetMapping("/{id}")
    public RestResponse getCouponById(@PathVariable Long id) {
        CouponDto coupon = couponService.getCouponById(id);
        return new RestResponse(true, coupon);
    }

    @GetMapping("/code/{couponCode}")
    public RestResponse getCouponByCode(@PathVariable String couponCode) {
        CouponDto coupon = couponService.getCouponByCode(couponCode);
        return new RestResponse(true, coupon);
    }

    @GetMapping("/type/{type}")
    public RestResponse getActiveAndValidCouponsByType(@PathVariable Coupon.CouponType type) {
        List<CouponDto> coupons = couponService.getActiveAndValidCouponsByType(type);
        return new RestResponse(true, coupons);
    }
}
