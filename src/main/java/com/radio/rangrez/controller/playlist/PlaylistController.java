package com.radio.rangrez.controller.playlist;

import com.radio.rangrez.dto.SongMetaData;
import com.radio.rangrez.dto.playlist.PlaylistQueryRequest;
import com.radio.rangrez.dto.playlist.PlaylistResponse;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.playlist.AIPlaylistService;
import com.radio.rangrez.service.song.MetaDataService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/playlist")
@Slf4j
public class PlaylistController {

    @Autowired
    private AIPlaylistService aiPlaylistService;

    @Autowired
    private MetaDataService metaDataService;

    @PostMapping("/generate")
    public RestResponse generatePlaylist(@Valid @RequestBody PlaylistQueryRequest request) {
        log.info("Received playlist generation request: {}", request.getUserQuery());

        PlaylistResponse playlist = aiPlaylistService.generatePlaylistFromQuery(request);

        return new RestResponse(true, playlist);
    }

    @PostMapping("/fetch-metadata")
    public RestResponse extractMetadata(@Valid @RequestBody PlaylistQueryRequest request) {
        log.info("Received metadata extraction request: {}", request.getUserQuery());

        SongMetaData metadata = metaDataService.extractMetadataFromQuery(request);

        return new RestResponse(true, metadata);
    }

}
