package com.radio.rangrez.controller.query;

import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.query.QueryGenerationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("/api/v1/public/queries")
@RestController
@Slf4j
public class PublicQueryController {

    @Autowired
    private QueryGenerationService queryGenerationService;

    @GetMapping("/generate")
    public RestResponse generateMusicQueries() {
        log.info("Received request to generate music query suggestions");
        
        List<String> queries = queryGenerationService.generateMusicQueries();
        
        log.info("Generated {} music query suggestions", queries.size());
        return new RestResponse(true, queries);
    }
}
