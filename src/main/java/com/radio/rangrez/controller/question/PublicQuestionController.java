package com.radio.rangrez.controller.question;

import com.radio.rangrez.dto.QuestionDto;
import com.radio.rangrez.dto.SubmitAllResponsesRequest;
import com.radio.rangrez.model.User;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.question.QuestionService;
import com.radio.rangrez.service.user.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/public/questions")
@RestController
public class PublicQuestionController {

    @Autowired
    private QuestionService questionService;

    @Autowired
    private UserService userService;

    @GetMapping
    public RestResponse getActiveQuestions() {
        List<QuestionDto> questions = questionService.getActiveQuestions();
        return new RestResponse(true, questions);
    }

    @PostMapping
    public RestResponse submitUserResponse(@Valid @RequestBody SubmitAllResponsesRequest request){
        User user = userService.getCurrentUser();
        questionService.submitAllResponses(request, user);
        return new RestResponse(true, "Responses saved successfully");
    }


}
