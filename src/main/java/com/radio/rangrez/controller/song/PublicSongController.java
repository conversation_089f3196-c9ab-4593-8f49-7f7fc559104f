package com.radio.rangrez.controller.song;

import com.radio.rangrez.dto.SongDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.song.SongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/public/songs")
@RestController
public class PublicSongController {

    @Autowired
    private SongService songService;

    @GetMapping
    public RestResponse getActiveSongs() {
        List<SongDto> songs = songService.getActiveSongs();
        return new RestResponse(true, songs);
    }

    @GetMapping("/paginated")
    public RestResponse getActiveSongs(Pageable pageable) {
        Page<SongDto> songs = songService.getAllSongs(pageable);
        return new RestResponse(true, songs);
    }

    @GetMapping("/{id}")
    public RestResponse getSongById(@PathVariable Long id) {
        SongDto song = songService.getSongById(id);
        return new RestResponse(true, song);
    }

    @GetMapping("/featured")
    public RestResponse getFeaturedSongs() {
        List<SongDto> songs = songService.getFeaturedSongs();
        return new RestResponse(true, songs);
    }

    @GetMapping("/popular")
    public RestResponse getPopularSongs() {
        List<SongDto> songs = songService.getPopularSongs();
        return new RestResponse(true, songs);
    }

    @GetMapping("/top-rated")
    public RestResponse getTopRatedSongs() {
        List<SongDto> songs = songService.getTopRatedSongs();
        return new RestResponse(true, songs);
    }

    @GetMapping("/search")
    public RestResponse searchSongs(@RequestParam String title) {
        List<SongDto> songs = songService.searchSongsByTitle(title);
        return new RestResponse(true, songs);
    }

    @GetMapping("/album/{albumName}")
    public RestResponse getSongsByAlbum(@PathVariable String albumName) {
        List<SongDto> songs = songService.getSongsByAlbum(albumName);
        return new RestResponse(true, songs);
    }

    @GetMapping("/language/{language}")
    public RestResponse getSongsByLanguage(@PathVariable String language) {
        List<SongDto> songs = songService.getSongsByLanguage(language);
        return new RestResponse(true, songs);
    }

    @GetMapping("/artist/{artistId}")
    public RestResponse getSongsByArtist(@PathVariable Long artistId) {
        List<SongDto> songs = songService.getSongsByArtist(artistId);
        return new RestResponse(true, songs);
    }

    @GetMapping("/genre/{genreId}")
    public RestResponse getSongsByGenre(@PathVariable Long genreId) {
        List<SongDto> songs = songService.getSongsByGenre(genreId);
        return new RestResponse(true, songs);
    }

    @GetMapping("/mood/{moodId}")
    public RestResponse getSongsByMood(@PathVariable Long moodId) {
        List<SongDto> songs = songService.getSongsByMood(moodId);
        return new RestResponse(true, songs);
    }

    @PostMapping("/{id}/play")
    public RestResponse incrementPlayCount(@PathVariable Long id) {
        SongDto updatedSong = songService.incrementPlayCount(id);
        return new RestResponse(true, updatedSong);
    }

    @PostMapping("/{id}/rate")
    public RestResponse rateSong(@PathVariable Long id, @RequestParam Double rating) {
        SongDto updatedSong = songService.rateSong(id, rating);
        return new RestResponse(true, updatedSong);
    }

    
}
