package com.radio.rangrez.controller.upload;


import com.radio.rangrez.enums.FileType;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.upload.S3Service;
import com.radio.rangrez.service.user.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/upload")
public class FileUploadController {

    private final S3Service s3Service;

    @Autowired
    private UserService userService;

    public FileUploadController(S3Service s3Service) {
        this.s3Service = s3Service;
    }

    @PostMapping
    public RestResponse uploadFile(@RequestPart("file") MultipartFile file,
                                   @RequestParam("type") FileType type) throws IOException {
        Map<String, String> result = s3Service.uploadFile(file, type);
        return new RestResponse(true, result);
    }
}