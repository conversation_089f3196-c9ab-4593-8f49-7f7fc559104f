package com.radio.rangrez.controller.upload;

import com.radio.rangrez.dto.upload.UploadRequest;
import com.radio.rangrez.dto.upload.UploadResponse;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.upload.UploadService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/api/v1/upload")
@RestController
public class UploadController {

    @Autowired
    private UploadService uploadService;

    @PostMapping("/presigned-url")
    public RestResponse generatePresignedUrl(@Valid @RequestBody UploadRequest request) {
        UploadResponse response = uploadService.generatePresignedUrl(request);
        return new RestResponse(true, response);
    }

    @DeleteMapping("/file/{fileName}")
    public RestResponse deleteFile(@PathVariable String fileName) {
        String decodedFileName = fileName.replace("__", "/");
        boolean deleted = uploadService.deleteFile(decodedFileName);
        return new RestResponse(deleted, deleted ? "File deleted successfully" : "Failed to delete file");
    }

    @PostMapping("/generate-filename")
    public RestResponse generateFileName(@RequestParam String fileExtension, 
                                       @RequestParam(required = false) String folder) {
        String fileName = uploadService.generateFileName(fileExtension, folder);
        return new RestResponse(true, fileName);
    }
}
