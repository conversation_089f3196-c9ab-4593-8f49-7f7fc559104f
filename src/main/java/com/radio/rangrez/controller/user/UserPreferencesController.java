package com.radio.rangrez.controller.user;

import com.radio.rangrez.dto.UserPreferencesDto;
import com.radio.rangrez.dto.UserPreferencesRequest;
import com.radio.rangrez.exception.ValidationException;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.user.UserPreferencesService;
import com.radio.rangrez.utils.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/api/v1/user/preferences")
@RestController
public class UserPreferencesController {

    @Autowired
    private UserPreferencesService userPreferencesService;

    @Autowired
    private JwtUtil jwtUtil;

    @GetMapping
    public RestResponse getUserPreferences(HttpServletRequest httpRequest) {
        String email = extractEmailFromToken(httpRequest);
        UserPreferencesDto preferences = userPreferencesService.getUserPreferences(email);
        return new RestResponse(true, preferences);
    }

    @PutMapping
    public RestResponse updateUserPreferences(@RequestBody UserPreferencesRequest request,
                                            HttpServletRequest httpRequest) {
        String email = extractEmailFromToken(httpRequest);
        UserPreferencesDto preferences = userPreferencesService.updateUserPreferences(email, request);
        return new RestResponse(true, preferences);
    }

    @PostMapping("/genres")
    public RestResponse addGenrePreferences(@RequestBody UserPreferencesRequest request,
                                          HttpServletRequest httpRequest) {
        String email = extractEmailFromToken(httpRequest);
        UserPreferencesDto preferences = userPreferencesService.addGenrePreferences(email, request);
        return new RestResponse(true, preferences);
    }

    @PostMapping("/moods")
    public RestResponse addMoodPreferences(@RequestBody UserPreferencesRequest request,
                                         HttpServletRequest httpRequest) {
        String email = extractEmailFromToken(httpRequest);
        UserPreferencesDto preferences = userPreferencesService.addMoodPreferences(email, request);
        return new RestResponse(true, preferences);
    }

    @DeleteMapping("/genres/{genreId}")
    public RestResponse removeGenrePreference(@PathVariable Long genreId,
                                            HttpServletRequest httpRequest) {
        String email = extractEmailFromToken(httpRequest);
        boolean removed = userPreferencesService.removeGenrePreference(email, genreId);
        return new RestResponse(removed, removed ? "Genre preference removed" : "Failed to remove genre preference");
    }

    @DeleteMapping("/moods/{moodId}")
    public RestResponse removeMoodPreference(@PathVariable Long moodId,
                                           HttpServletRequest httpRequest) {
        String email = extractEmailFromToken(httpRequest);
        boolean removed = userPreferencesService.removeMoodPreference(email, moodId);
        return new RestResponse(removed, removed ? "Mood preference removed" : "Failed to remove mood preference");
    }

    private String extractEmailFromToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            return jwtUtil.extractEmail(token);
        }
        throw new ValidationException(ValidationException.ValidationExceptionType.INVALID_TOKEN);
    }
}
