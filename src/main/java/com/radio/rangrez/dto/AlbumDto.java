package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlbumDto {

    private Long id;
    private String title;
    private String description;
    private String coverImageUrl;
    private LocalDate releaseDate;
    private Integer totalTracks;
    private Integer totalDurationSeconds;
    private ArtistDto primaryArtist;
    private List<AlbumSongDto> songs;
    private boolean activated;
}
