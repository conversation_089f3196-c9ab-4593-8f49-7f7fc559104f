package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ArtistDto {

    private Long id;
    private String fullName;
    private String stageName;
    private String artistImageUrl;
    private String biography;
    private ArtistPreferences artistPreferences;
    private String countryRegion;
    private String contactEmail;
    private String contactPhone;
    private List<GenreDto> genres;
    private LocalDateTime created;
    private LocalDateTime updated;
    private Boolean activated = true;

}
