package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.radio.rangrez.model.Coupon;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CouponDto {

    private Long id;
    private String name;
    private String image;
    private String description;
    private String shortDescription;
    private String couponCode;
    private LocalDateTime expireTime;
    private Integer priority;
    private Coupon.CouponType type;
    private LocalDateTime created;
    private LocalDateTime updated;
    private boolean activated;
    private boolean isExpired;
}
