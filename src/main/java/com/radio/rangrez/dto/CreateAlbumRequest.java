package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateAlbumRequest {

    @NotBlank(message = "Album title is required")
    private String title;

    private String description;
    private String coverImageUrl;
    private LocalDate releaseDate;

    @NotNull(message = "Primary artist is required")
    private Long primaryArtistId;

    private List<AlbumSongRequest> songs;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AlbumSongRequest {
        @NotNull(message = "Song ID is required")
        private Long songId;
        
        @NotNull(message = "Track number is required")
        private Integer trackNumber;
    }
}
