package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateMoodRequest {

    @NotBlank(message = "Mood name is required")
    private String name;

    private String description;
    private String iconUrl;
    private String colorCode;
    private Integer displayOrder;
}
