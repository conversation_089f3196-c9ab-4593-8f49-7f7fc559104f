package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.radio.rangrez.enums.QuestionType;
import com.radio.rangrez.model.Question;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateQuestionRequest {

    @NotBlank(message = "Question text is required")
    private String questionText;

    @NotNull(message = "Question type is required")
    private QuestionType questionType;

    private Integer displayOrder;
    private Boolean isRequired;
    private String description;

    private List<CreateQuestionOptionRequest> options;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CreateQuestionOptionRequest {
        @NotBlank(message = "Option text is required")
        private String optionText;
        private String optionValue;
        private Integer displayOrder;
        private String iconUrl;
        private String colorCode;
    }
}
