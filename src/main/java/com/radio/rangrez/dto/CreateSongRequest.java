package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.radio.rangrez.model.SongArtist;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateSongRequest {

    @NotBlank(message = "Song title is required")
    private String title;

    private Integer durationSeconds;
    private String audioFileUrl;
    private String coverImageUrl;
    private String albumName;
    private Integer trackNumber;
    private LocalDate releaseDate;
    private String lyrics;
    private String language;
    private boolean isExplicit = false;
    private boolean isFeatured = false;

    @NotEmpty(message = "At least one artist is required")
    private List<SongArtistRequest> artists;

    @NotEmpty(message = "At least one genre is required")
    private List<Long> genreIds;
    private List<Long> moodIds;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SongArtistRequest {
        private Long artistId;
        private SongArtist.ArtistRole role = SongArtist.ArtistRole.MAIN_ARTIST;
    }
}
