package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MoodDto {

    private Long id;
    private String name;
    private String description;
    private String iconUrl;
    private String colorCode;
    private Integer displayOrder;
    private LocalDateTime created;
    private LocalDateTime updated;
    private boolean activated;
}
