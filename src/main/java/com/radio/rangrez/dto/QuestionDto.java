package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.radio.rangrez.enums.QuestionType;
import com.radio.rangrez.model.Question;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QuestionDto {

    private Long id;
    private String questionText;
    private QuestionType questionType;
    private Integer displayOrder;
    private Boolean isRequired;
    private String description;
    private List<QuestionOptionDto> options;
    private LocalDateTime created;
    private LocalDateTime updated;
    private boolean activated;
}
