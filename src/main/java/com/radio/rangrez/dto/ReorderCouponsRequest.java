package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReorderCouponsRequest {

    @NotEmpty(message = "Coupon order list cannot be empty")
    @Valid
    private List<CouponOrderDTO> coupons;
}
