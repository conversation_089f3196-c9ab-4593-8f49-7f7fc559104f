package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SongDto {

    private Long id;
    private String title;
    private Integer durationSeconds;
    private String audioFileUrl;
    private String coverImageUrl;
    private String albumName;
    private Integer trackNumber;
    private LocalDate releaseDate;
    private String lyrics;
    private String language;
    private Long playCount;
    private Double averageRating;
    private Long totalRatings;
    private boolean isExplicit;
    private boolean isFeatured;
    private List<SongArtistDto> artists;
    private List<GenreDto> genres;
    private List<MoodDto> moods;
    private LocalDateTime created;
    private LocalDateTime updated;
    private boolean activated;
}
