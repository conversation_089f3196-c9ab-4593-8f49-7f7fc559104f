package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SongMetaData {
    private String title;
    private String artist;
    private String album;
    private String genre;
    private String language;
    private String mood;
    private List<String> moods;
    private String era;
    private String spotifyArtistId;
    private String country;
    private String region;
    private String tempo;
    private String popularity;
    private String activity;
    private String coverImageUrl;
    private String audioPreviewUrl;
}
