package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.radio.rangrez.model.SongArtist;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateSongArtistsRequest {

    @NotEmpty(message = "At least one artist is required")
    private List<SongArtistRequest> artists;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SongArtistRequest {
        private Long artistId;
        private SongArtist.ArtistRole role = SongArtist.ArtistRole.MAIN_ARTIST;
    }
}
