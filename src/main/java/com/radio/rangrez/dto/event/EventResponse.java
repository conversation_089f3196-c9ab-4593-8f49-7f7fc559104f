package com.radio.rangrez.dto.event;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventResponse {

    private EventDto event;
    private String googleCalendarHtmlLink;
    private String message;
    private boolean syncedWithGoogleCalendar;

    public EventResponse(EventDto event, String googleCalendarHtmlLink) {
        this.event = event;
        this.googleCalendarHtmlLink = googleCalendarHtmlLink;
        this.syncedWithGoogleCalendar = googleCalendarHtmlLink != null;
    }

    public EventResponse(EventDto event, String message, boolean syncedWithGoogleCalendar) {
        this.event = event;
        this.message = message;
        this.syncedWithGoogleCalendar = syncedWithGoogleCalendar;
    }
}
