package com.radio.rangrez.dto.playlist;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlaylistMetadata {
    
    private String genre;
    private List<String> sub_genres;
    private String language;
    private List<String> artist;
    private String mood;
    private String activity;
    private String era;
    private Integer count;
    private Integer duration;
    private String tempo;
    private Boolean explicit;
    private String popularity;
    private List<String> seed_tracks;
    private List<String> seed_albums;
    private String region;
}
