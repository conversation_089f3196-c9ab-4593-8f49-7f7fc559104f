package com.radio.rangrez.dto.upload;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.radio.rangrez.enums.FileType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UploadRequest {

    @NotBlank(message = "File extension is required")
    @Pattern(regexp = "^\\.(jpg|jpeg|png|gif|mp3|wav|flac|pdf|doc|docx)$", 
             message = "Unsupported file type. Allowed: jpg, jpeg, png, gif, mp3, wav, flac, pdf, doc, docx")
    private String fileExtension;

    private FileType folder; // Optional: images, audio, documents, etc.
}
