package com.radio.rangrez.dto.upload;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UploadResponse {

    private String presignedUrl;
    private String fileName;
    private String fileUrl; // Final URL after upload
    private String uploadMethod; // PUT or POST
    private LocalDateTime expiresAt;
    private Long maxFileSize; // in bytes
    private String contentType;

    public UploadResponse(String presignedUrl, String fileName, String fileUrl, LocalDateTime expiresAt) {
        this.presignedUrl = presignedUrl;
        this.fileName = fileName;
        this.fileUrl = fileUrl;
        this.expiresAt = expiresAt;
        this.uploadMethod = "PUT";
    }
}
