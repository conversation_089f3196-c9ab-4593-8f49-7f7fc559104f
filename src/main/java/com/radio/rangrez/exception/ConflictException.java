package com.radio.rangrez.exception;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
public class ConflictException extends BaseException {

    public ConflictException(ConflictExceptionType conflictExceptionType) {
        super(409, conflictExceptionType.getErrorKey(), conflictExceptionType.getStatusCode(),
                conflictExceptionType.getErrorMessage(), conflictExceptionType.getDeveloperMessage(),
                conflictExceptionType.getDefaultMessage(), conflictExceptionType.getDefaultMessageParamMap());
    }

    @Getter
    public enum ConflictExceptionType {
        MOOD_ALREADY_EXISTS("409001", "Mo<PERSON> with this name already exists", "<PERSON><PERSON> with this name already exists"),
        GENRE_ALREADY_EXISTS("409002", "Genre with this name already exists", "Genre with this name already exists"),
        COUPON_CODE_ALREADY_EXISTS("409003", "Coupon with this code already exists", "Coupon with this code already exists"),
        SONG_ALREADY_IN_ALBUM("409004", "Song already exists in this album", "Song already exists in this album");

        private String errorKey;
        private final String statusCode;
        @Setter
        private String errorMessage;
        @Setter
        private String developerMessage;
        @Setter
        private String defaultMessage;
        @Setter
        private Map<String, String> defaultMessageParamMap;

        ConflictExceptionType(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }

        ConflictExceptionType(String errorKey, String statusCode, String errorMessage, String developerMessage) {
            this.errorKey = errorKey;
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }


    }
}
