package com.radio.rangrez.exception;

import jakarta.annotation.Nullable;

public class MaximumUploadSizeException extends BaseException {

    public MaximumUploadSizeException(long maxUploadSize) {
        this(maxUploadSize, null);
    }

    public MaximumUploadSizeException(long maxUploadSize, @Nullable Throwable ex) {
        super(413, null, "413000",
              "Maximum upload size " + (maxUploadSize >= 0L ? "of " + maxUploadSize + "MB " : "") + "exceeded",
              "Maximum upload size " + (maxUploadSize >= 0L ? "of " + maxUploadSize + "MB " : "") + "exceeded",
              null, null);
    }
}
