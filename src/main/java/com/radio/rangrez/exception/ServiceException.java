package com.radio.rangrez.exception;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
public class ServiceException extends BaseException {

    public ServiceException(ServiceExceptionType serviceExceptionType) {
        super(500, serviceExceptionType.getErrorKey(), serviceExceptionType.getStatusCode(),
                serviceExceptionType.getErrorMessage(), serviceExceptionType.getDeveloperMessage(),
                serviceExceptionType.getDefaultMessage(), serviceExceptionType.getDefaultMessageParamMap());
    }

    public ServiceException(String message) {
        super(500, null, "500000", message, message, null, null);
    }

    @Getter
    public enum ServiceExceptionType {
        UPLOAD_SERVICE_ERROR("500001", "Failed to generate presigned URL", "Failed to generate presigned URL"),
        COGNITO_SERVICE_ERROR("500002", "Failed to communicate with Cognito service", "Failed to communicate with Cognito service"),
        EVENT_CREATION_ERROR("500003", "Failed to create event", "Failed to create event"),
        CALENDAR_SYNC_ERROR("500004", "Failed to sync with Google Calendar", "Failed to sync with Google Calendar"),
        JSON_CONVERSION_ERROR("500005", "Error converting data to JSON", "Error converting data to JSON"),
        FILE_DELETE_ERROR("500006", "Failed to delete file", "Failed to delete file"),
        CRYPTO_ERROR("500007", "Error while calculating secret hash", "Error while calculating secret hash"),
        EVENT_UPDATE_ERROR("500008", "Failed to update event", "Failed to update event"),
        COGNITO_SIGN_IN_ERROR("500009", "Failed to sign in", "Failed to sign in"),
        COGNITO_FORGOT_PASSWORD_ERROR("500010", "Failed to send password reset OTP", "Failed to send password reset OTP"),
        COGNITO_VERIFY_OTP_ERROR("500011", "Failed to verify OTP", "Failed to verify OTP"),
        COGNITO_USER_STATUS_ERROR("500012", "User is in unexpected status", "User is in unexpected status"),
        COGNITO_OTP_EXPIRED_ERROR("500013", "OTP expired. Please request a new one", "OTP expired. Please request a new one"),
        COGNITO_USER_NOT_FOUND_ERROR("500014", "User does not exist", "User does not exist");

        private String errorKey;
        private final String statusCode;
        private final String errorMessage;
        private final String developerMessage;
        private String defaultMessage;
        private Map<String, String> defaultMessageParamMap;

        ServiceExceptionType(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }
    }
}
