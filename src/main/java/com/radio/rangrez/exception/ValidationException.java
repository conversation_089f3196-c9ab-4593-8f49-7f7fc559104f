package com.radio.rangrez.exception;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
public class ValidationException extends BaseException {

    public ValidationException(ValidationExceptionType validationExceptionType) {
        super(400, validationExceptionType.getErrorKey(), validationExceptionType.getStatusCode(),
                validationExceptionType.getErrorMessage(), validationExceptionType.getDeveloperMessage(),
                validationExceptionType.getDefaultMessage(), validationExceptionType.getDefaultMessageParamMap());
    }

    public ValidationException(String message) {
        super(400, null, "400000", message, message, null, null);
    }

    @Getter
    public enum ValidationExceptionType {
        INVALID_RATING("400001", "Rating must be between 1.0 and 5.0", "Rating must be between 1.0 and 5.0"),
        INVALID_EXPIRE_TIME("400002", "Expire time must be in the future", "Expire time must be in the future"),
        INVALID_OPTION_FOR_QUESTION("400003", "Option does not belong to the specified question", "Option does not belong to the specified question"),
        INVALID_TRACK_NUMBER("400004", "Track number already exists in this album", "Track number already exists in this album"),
        INVALID_TOKEN("400005", "No valid token found", "No valid token found"),
        INVALID_PRIORITY("400006", "Priority must be within valid range", "Priority must be within valid range"),
        MAXIMUM_UPLOAD_SIZE_EXCEEDED("400007", "Maximum upload size exceeded", "Maximum upload size exceeded");

        private String errorKey;
        private final String statusCode;
        private final String errorMessage;
        private final String developerMessage;
        private String defaultMessage;
        private Map<String, String> defaultMessageParamMap;

        ValidationExceptionType(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }
    }
}
