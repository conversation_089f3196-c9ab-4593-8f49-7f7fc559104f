package com.radio.rangrez.model;


import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

@MappedSuperclass
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public abstract class BaseEntity implements Serializable {

    @Column(name = "created_date")
    @CreationTimestamp
    protected LocalDateTime created;

    @Column(name = "updated_date")
    @UpdateTimestamp
    protected LocalDateTime updated;

    @Column(name = "activated")
    protected boolean activated = true;

}
