package com.radio.rangrez.model;

import com.radio.rangrez.enums.EventStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "events")
@Entity
public class Event extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "title", nullable = false)
    private String title;

    @Column(name = "description", columnDefinition = "text")
    private String description;

    @Column(name = "start_date_time", nullable = false)
    private LocalDateTime startDateTime;

    @Column(name = "end_date_time", nullable = false)
    private LocalDateTime endDateTime;

    @Column(name = "live_radio_url")
    private String liveRadioUrl;

    @Column(name = "location")
    private String location;

    @Column(name = "attendees", columnDefinition = "text")
    private String attendees; // JSON string of email addresses

    @Column(name = "google_calendar_event_id")
    private String googleCalendarEventId;

    @Column(name = "google_calendar_html_link")
    private String googleCalendarHtmlLink;

    @Column(name = "calendar_id")
    private String calendarId; // Google Calendar ID (default: 'primary')

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "event_status")
    @Enumerated(EnumType.STRING)
    private EventStatus eventStatus = EventStatus.CONFIRMED;

    @Column(name = "timezone")
    private String timezone = "UTC";
}
