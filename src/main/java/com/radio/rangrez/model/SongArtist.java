package com.radio.rangrez.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "song_artists")
@Entity
public class SongArtist extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "song_id", nullable = false)
    private Song song;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "artist_id", nullable = false)
    private Artist artist;

    @Column(name = "role")
    @Enumerated(EnumType.STRING)
    private ArtistRole role = ArtistRole.MAIN_ARTIST;

    public enum ArtistRole {
        MAIN_ARTIST,
        FEATURED_ARTIST,
        COMPOSER,
        LYRICIST,
        PRODUCER
    }
}
