package com.radio.rangrez.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "user_question_responses")
@Entity
@Builder
public class UserQuestionResponse extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Which user submitted the response
    @Column(name = "user_id", nullable = false)
    private Long userId;

    // Link to the answered question (many responses can belong to the same question)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", nullable = false)
    private Question question;

    // Options selected by the user (can be multiple for MULTI_SELECT)
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "user_response_options", // This will be a new join table
            joinColumns = @JoinColumn(name = "response_id"),  // FK to this entity
            inverseJoinColumns = @JoinColumn(name = "question_option_id")  // FK to question_option
    )
    private List<QuestionOption> selectedOptions;

    @Column(name = "custom_response", columnDefinition = "text")
    private String customResponse;
}
