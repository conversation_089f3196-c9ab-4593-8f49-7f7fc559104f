package com.radio.rangrez.repository;

import com.radio.rangrez.model.Album;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AlbumRepository extends JpaRepository<Album, Long> {
    
    List<Album> findByActivatedTrueOrderByReleaseDateDescTitleAsc();
    
    List<Album> findByPrimaryArtistIdAndActivatedTrueOrderByReleaseDateDesc(Long artistId);
    
    List<Album> findByTitleContainingIgnoreCaseAndActivatedTrue(String title);
    
    @Query("SELECT a FROM Album a WHERE a.activated = true AND a.releaseDate IS NOT NULL ORDER BY a.releaseDate DESC")
    List<Album> findActiveAlbumsWithReleaseDate();

    List<Album> findByActivatedTrue();
}
