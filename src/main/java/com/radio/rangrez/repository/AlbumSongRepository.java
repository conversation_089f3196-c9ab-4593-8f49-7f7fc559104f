package com.radio.rangrez.repository;

import com.radio.rangrez.model.AlbumSong;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AlbumSongRepository extends JpaRepository<AlbumSong, Long> {
    
    List<AlbumSong> findByAlbumIdOrderByTrackNumberAsc(Long albumId);
    
    Optional<AlbumSong> findByAlbumIdAndSongId(Long albumId, Long songId);
    
    boolean existsByAlbumIdAndTrackNumber(Long albumId, Integer trackNumber);
    
    void deleteByAlbumIdAndSongId(Long albumId, Long songId);
}
