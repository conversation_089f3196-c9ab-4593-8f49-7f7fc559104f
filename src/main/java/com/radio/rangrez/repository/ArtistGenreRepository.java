package com.radio.rangrez.repository;

import com.radio.rangrez.model.ArtistGenre;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ArtistGenreRepository extends JpaRepository<ArtistGenre, Long> {
    
    List<ArtistGenre> findByArtistId(Long artistId);
    
    List<ArtistGenre> findByGenreId(Long genreId);
    
    void deleteByArtistId(Long artistId);
    
    void deleteByArtistIdAndGenreId(Long artistId, Long genreId);
    
    boolean existsByArtistIdAndGenreId(Long artistId, Long genreId);
    
    @Query("SELECT ag.genre FROM ArtistGenre ag WHERE ag.artist.id = :artistId AND ag.genre.activated = true")
    List<com.radio.rangrez.model.Genre> findGenresByArtistId(Long artistId);
}
