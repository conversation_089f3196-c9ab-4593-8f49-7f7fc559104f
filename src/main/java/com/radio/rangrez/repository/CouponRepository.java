package com.radio.rangrez.repository;

import com.radio.rangrez.model.Coupon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface CouponRepository extends JpaRepository<Coupon, Long> {
    
    List<Coupon> findByActivatedTrueOrderByPriorityAscNameAsc();
    
    Optional<Coupon> findByCouponCodeIgnoreCase(String couponCode);
    
    boolean existsByCouponCodeIgnoreCase(String couponCode);
    
    List<Coupon> findByActivatedTrueOrderByPriorityAsc();
    
    @Query("SELECT c FROM Coupon c WHERE c.activated = true AND c.expireTime > :currentTime ORDER BY c.priority ASC, c.name ASC")
    List<Coupon> findActiveAndNotExpiredCoupons(LocalDateTime currentTime);
    
    @Query("SELECT c FROM Coupon c WHERE c.activated = true AND c.expireTime <= :currentTime ORDER BY c.priority ASC, c.name ASC")
    List<Coupon> findActiveAndExpiredCoupons(LocalDateTime currentTime);

    List<Coupon> findByActivatedTrueAndPriorityGreaterThanEqualOrderByPriorityAsc(Integer priority);

    List<Coupon> findByActivatedTrueAndPriorityBetweenOrderByPriorityAsc(Integer startPriority, Integer endPriority);

    @Query("SELECT MAX(c.priority) FROM Coupon c WHERE c.activated = true")
    Integer findMaxPriorityForActiveCoupons();

    List<Coupon> findByActivatedTrueAndTypeOrderByPriorityAscNameAsc(Coupon.CouponType type);

    @Query("SELECT c FROM Coupon c WHERE c.activated = true AND c.type = :type AND c.expireTime > :currentTime ORDER BY c.priority ASC, c.name ASC")
    List<Coupon> findActiveAndNotExpiredCouponsByType(Coupon.CouponType type, LocalDateTime currentTime);
}
