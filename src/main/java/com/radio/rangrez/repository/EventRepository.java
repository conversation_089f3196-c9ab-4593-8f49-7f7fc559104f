package com.radio.rangrez.repository;

import com.radio.rangrez.enums.EventStatus;
import com.radio.rangrez.model.Event;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface EventRepository extends JpaRepository<Event, Long> {
    
    List<Event> findByActivatedTrueOrderByStartDateTimeAsc();
    
    List<Event> findByCreatedByAndActivatedTrueOrderByStartDateTimeAsc(String createdBy);
    
    Optional<Event> findByGoogleCalendarEventIdAndActivatedTrue(String googleCalendarEventId);
    
    @Query("SELECT e FROM Event e WHERE e.activated = true AND e.startDateTime >= :startDate AND e.endDateTime <= :endDate ORDER BY e.startDateTime ASC")
    List<Event> findEventsBetweenDates(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT e FROM Event e WHERE e.activated = true AND e.startDateTime >= :currentDateTime ORDER BY e.startDateTime ASC")
    List<Event> findUpcomingEvents(@Param("currentDateTime") LocalDateTime currentDateTime);
    
    @Query("SELECT e FROM Event e WHERE e.activated = true AND e.endDateTime < :currentDateTime ORDER BY e.startDateTime DESC")
    List<Event> findPastEvents(@Param("currentDateTime") LocalDateTime currentDateTime);
    
    List<Event> findByEventStatusAndActivatedTrue(EventStatus eventStatus);
}
