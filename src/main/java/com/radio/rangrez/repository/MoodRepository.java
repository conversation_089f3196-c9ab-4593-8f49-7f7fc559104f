package com.radio.rangrez.repository;

import com.radio.rangrez.model.Mood;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MoodRepository extends JpaRepository<Mood, Long> {
    
    List<Mood> findByActivatedTrueOrderByDisplayOrderAscNameAsc();
    
    Optional<Mood> findByNameIgnoreCase(String name);
    
    boolean existsByNameIgnoreCase(String name);
    
    List<Mood> findByActivatedTrueOrderByDisplayOrderAsc();
}
