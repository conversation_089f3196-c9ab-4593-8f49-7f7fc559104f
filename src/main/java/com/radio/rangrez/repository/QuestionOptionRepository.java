package com.radio.rangrez.repository;

import com.radio.rangrez.model.QuestionOption;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QuestionOptionRepository extends JpaRepository<QuestionOption, Long> {
    
    List<QuestionOption> findByQuestionIdAndActivatedTrueOrderByDisplayOrderAsc(Long questionId);
    
    List<QuestionOption> findByQuestionIdOrderByDisplayOrderAsc(Long questionId);
    
    void deleteByQuestionId(Long questionId);
}
