package com.radio.rangrez.repository;

import com.radio.rangrez.model.Question;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QuestionRepository extends JpaRepository<Question, Long> {
    
    List<Question> findByActivatedTrueOrderByDisplayOrderAsc();

    List<Question> findByActivatedTrueOrderByDisplayOrderAscCreatedAsc();
}
