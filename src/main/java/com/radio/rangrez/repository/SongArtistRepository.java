package com.radio.rangrez.repository;

import com.radio.rangrez.model.SongArtist;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SongArtistRepository extends JpaRepository<SongArtist, Long> {
    
    List<SongArtist> findBySongId(Long songId);
    
    List<SongArtist> findByArtistId(Long artistId);
    
    void deleteBySongId(Long songId);
    
    void deleteBySongIdAndArtistId(Long songId, Long artistId);
    
    boolean existsBySongIdAndArtistId(Long songId, Long artistId);
    
    @Query("SELECT sa.artist FROM SongArtist sa WHERE sa.song.id = :songId")
    List<com.radio.rangrez.model.Artist> findArtistsBySongId(Long songId);
    
    @Query("SELECT sa.song FROM SongArtist sa WHERE sa.artist.id = :artistId AND sa.song.activated = true")
    List<com.radio.rangrez.model.Song> findSongsByArtistId(Long artistId);
}
