package com.radio.rangrez.repository;

import com.radio.rangrez.model.SongGenre;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SongGenreRepository extends JpaRepository<SongGenre, Long> {
    
    List<SongGenre> findBySongId(Long songId);
    
    List<SongGenre> findByGenreId(Long genreId);
    
    void deleteBySongId(Long songId);
    
    void deleteBySongIdAndGenreId(Long songId, Long genreId);
    
    boolean existsBySongIdAndGenreId(Long songId, Long genreId);
    
    @Query("SELECT sg.genre FROM SongGenre sg WHERE sg.song.id = :songId AND sg.genre.activated = true")
    List<com.radio.rangrez.model.Genre> findGenresBySongId(Long songId);
    
    @Query("SELECT sg.song FROM SongGenre sg WHERE sg.genre.id = :genreId AND sg.song.activated = true")
    List<com.radio.rangrez.model.Song> findSongsByGenreId(Long genreId);
}
