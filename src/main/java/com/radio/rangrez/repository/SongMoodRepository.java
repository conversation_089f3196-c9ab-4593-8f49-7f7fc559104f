package com.radio.rangrez.repository;

import com.radio.rangrez.model.SongMood;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SongMoodRepository extends JpaRepository<SongMood, Long> {
    
    List<SongMood> findBySongId(Long songId);
    
    List<SongMood> findByMoodId(Long moodId);
    
    void deleteBySongId(Long songId);
    
    void deleteBySongIdAndMoodId(Long songId, Long moodId);
    
    boolean existsBySongIdAndMoodId(Long songId, Long moodId);
    
    @Query("SELECT sm.mood FROM SongMood sm WHERE sm.song.id = :songId AND sm.mood.activated = true")
    List<com.radio.rangrez.model.Mood> findMoodsBySongId(Long songId);
    
    @Query("SELECT sm.song FROM SongMood sm WHERE sm.mood.id = :moodId AND sm.song.activated = true")
    List<com.radio.rangrez.model.Song> findSongsByMoodId(Long moodId);
}
