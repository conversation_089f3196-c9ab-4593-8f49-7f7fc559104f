package com.radio.rangrez.repository;

import com.radio.rangrez.model.Song;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SongRepository extends JpaRepository<Song, Long> {
    
    List<Song> findByActivatedTrueOrderByCreatedDesc();
    
    Page<Song> findByActivatedTrue(Pageable pageable);
    
    List<Song> findByTitleContainingIgnoreCaseAndActivatedTrue(String title);
    
    List<Song> findByIsFeaturedTrueAndActivatedTrueOrderByPlayCountDesc();
    
    List<Song> findTop10ByActivatedTrueOrderByPlayCountDesc();
    
    List<Song> findTop10ByActivatedTrueOrderByAverageRatingDesc();
    
    @Query("SELECT s FROM Song s WHERE s.activated = true AND s.playCount > :minPlayCount ORDER BY s.playCount DESC")
    List<Song> findPopularSongs(@Param("minPlayCount") Long minPlayCount);
    
    @Query("SELECT s FROM Song s WHERE s.activated = true AND s.averageRating >= :minRating ORDER BY s.averageRating DESC")
    List<Song> findHighRatedSongs(@Param("minRating") Double minRating);

    List<Song> findByAlbumNameContainingIgnoreCaseAndActivatedTrue(String albumName);

    List<Song> findByLanguageContainingIgnoreCaseAndActivatedTrue(String language);
}
