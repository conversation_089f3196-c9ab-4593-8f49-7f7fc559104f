package com.radio.rangrez.repository;

import com.radio.rangrez.model.UserGenrePreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserGenrePreferenceRepository extends JpaRepository<UserGenrePreference, Long> {
    
    List<UserGenrePreference> findByUserId(Long userId);
    
    List<UserGenrePreference> findByGenreId(Long genreId);
    
    void deleteByUserId(Long userId);
    
    void deleteByUserIdAndGenreId(Long userId, Long genreId);
    
    boolean existsByUserIdAndGenreId(Long userId, Long genreId);
    
    @Query("SELECT ugp.genre FROM UserGenrePreference ugp WHERE ugp.user.id = :userId AND ugp.genre.activated = true")
    List<com.radio.rangrez.model.Genre> findGenresByUserId(Long userId);
}
