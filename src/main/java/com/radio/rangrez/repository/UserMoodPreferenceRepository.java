package com.radio.rangrez.repository;

import com.radio.rangrez.model.UserMoodPreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserMoodPreferenceRepository extends JpaRepository<UserMoodPreference, Long> {
    
    List<UserMoodPreference> findByUserId(Long userId);
    
    List<UserMoodPreference> findByMoodId(Long moodId);
    
    void deleteByUserId(Long userId);
    
    void deleteByUserIdAndMoodId(Long userId, Long moodId);
    
    boolean existsByUserIdAndMoodId(Long userId, Long moodId);
    
    @Query("SELECT ump.mood FROM UserMoodPreference ump WHERE ump.user.id = :userId AND ump.mood.activated = true")
    List<com.radio.rangrez.model.Mood> findMoodsByUserId(Long userId);
}
