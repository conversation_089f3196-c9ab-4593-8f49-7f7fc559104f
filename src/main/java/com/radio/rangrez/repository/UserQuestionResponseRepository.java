package com.radio.rangrez.repository;

import com.radio.rangrez.model.UserQuestionResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserQuestionResponseRepository extends JpaRepository<UserQuestionResponse, Long> {
    
    List<UserQuestionResponse> findByUserId(Long userId);
    
    List<UserQuestionResponse> findByUserIdAndQuestionId(Long userId, Long questionId);
    
    void deleteByUserIdAndQuestionId(Long userId, Long questionId);
}
