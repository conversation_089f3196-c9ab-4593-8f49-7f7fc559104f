package com.radio.rangrez.repository;

import com.radio.rangrez.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    Optional<User> findByEmail(String email);
    
    Optional<User> findByCognitoUserId(String cognitoUserId);
    
    boolean existsByEmail(String email);
}
