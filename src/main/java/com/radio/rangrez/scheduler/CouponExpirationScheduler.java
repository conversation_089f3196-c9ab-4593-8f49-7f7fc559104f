package com.radio.rangrez.scheduler;

import com.radio.rangrez.service.coupon.CouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Scheduler service to automatically expire and deactivate coupons
 * Runs every hour to check for expired coupons and deactivate them
 */
@Component
@Slf4j
public class CouponExpirationScheduler {

    @Autowired
    private CouponService couponService;

    /**
     * Scheduled job that runs every hour to expire and deactivate coupons
     * Cron expression: "0 0 * * * *" means run at the top of every hour
     */
    @Scheduled(cron = "0 0 * * * *")
    public void expireAndDeactivateCoupons() {
        log.info("Starting coupon expiration job at {}", LocalDateTime.now());

        try {
            int deactivatedCount = couponService.expireAndDeactivateCoupons();

            if (deactivatedCount == 0) {
                log.info("No expired coupons found to deactivate");
            } else {
                log.info("Successfully deactivated {} expired coupons", deactivatedCount);
            }

        } catch (Exception e) {
            log.error("Error occurred while processing expired coupons: {}", e.getMessage(), e);
        }

        log.info("Completed coupon expiration job at {}", LocalDateTime.now());
    }
    
    /**
     * Alternative method for manual testing - can be called via REST endpoint if needed
     * This method provides the same functionality but can be triggered manually
     */
    public int manuallyExpireCoupons() {
        log.info("Manual coupon expiration triggered at {}", LocalDateTime.now());

        int deactivatedCount = couponService.expireAndDeactivateCoupons();

        if (deactivatedCount == 0) {
            log.info("No expired coupons found for manual expiration");
        } else {
            log.info("Manually deactivated {} expired coupons", deactivatedCount);
        }

        return deactivatedCount;
    }
}
