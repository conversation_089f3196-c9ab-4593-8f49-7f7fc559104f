package com.radio.rangrez.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.radio.rangrez.model.response.RestError;
import com.radio.rangrez.model.response.RestResponse;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class AuthAccessDeniedHandler implements AccessDeniedHandler {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException, ServletException {

        RestResponse res = new RestResponse(false);
        res.setError(new RestError(401, accessDeniedException.getMessage()));

        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.getWriter().append(objectMapper.writeValueAsString(res));
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
    }

}
