package com.radio.rangrez.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.radio.rangrez.constants.Constants;
import com.radio.rangrez.exception.RateLimitExceededException;
import com.radio.rangrez.model.response.RestError;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.utils.JwtUtil;
import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.ConsumptionProbe;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Duration;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class AuthenticationTokenFilter extends OncePerRequestFilter {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final TokenUtils tokenUtils;
    private final JwtUtil jwtUtil;
    private final Map<String, Bucket> buckets = new ConcurrentHashMap<>();
    @Setter
    private UserDetailsService userDetailsService;
    @Value("${rate.limit.enabled}")
    private boolean rateLimitEnabled;
    @Value("${rate.limit.request.limit}")
    private Integer rateLimitRequestLimit;
    @Value("${rate.limit.request.time}")
    private Integer rateLimitRequestTime;

    public AuthenticationTokenFilter(TokenUtils tokenUtils, JwtUtil jwtUtil) {
        this.tokenUtils = tokenUtils;
        this.jwtUtil = jwtUtil;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest httpRequest, HttpServletResponse httpResponse, FilterChain chain) throws IOException {

        String reqId = UUID.randomUUID().toString().toUpperCase().replace("-", "");
        try {
            //put requestId in MDC
            MDC.put("requestId", reqId);

            String authToken = null;
            String userName = null;

            // If Regular User with JWT Token
            String header = httpRequest.getHeader("Authorization");
            if (header != null && header.startsWith("Bearer ")) {
                authToken = header.substring(7);
                //get email from JWT token
                try {
                    userName = jwtUtil.extractEmail(authToken);
                } catch (Exception e) {
                    // If JWT fails, try old token utils for backward compatibility
                    userName = tokenUtils.getUserNameFromToken(authToken);
                }
            }
            applyRateLimit(header, httpRequest);
            if (userName != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                UserDetails userDetails = userDetailsService.loadUserByUsername(userName);

                MDC.put(Constants.USER_NAME, userDetails.getUsername());

                // Try JWT validation first, then fall back to old token validation
                boolean isValidToken = false;
                try {
                    isValidToken = jwtUtil.validateToken(authToken, userName);
                } catch (Exception e) {
                    isValidToken = Boolean.TRUE.equals(tokenUtils.validateToken(authToken, userDetails));
                }

                if (isValidToken) {
                    UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(
                            userDetails, null, userDetails.getAuthorities());
                    usernamePasswordAuthenticationToken
                            .setDetails(new WebAuthenticationDetailsSource().buildDetails(httpRequest));
                    SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
                }
            }
            chain.doFilter(httpRequest, httpResponse);
        } catch (RateLimitExceededException ex) {
            RestResponse res = new RestResponse(false);
            res.setError(new RestError(429, ex.getMessage()));

            httpResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
            httpResponse.getWriter().append(objectMapper.writeValueAsString(res));
            httpResponse.setStatus(429);
        } catch (Exception ex) {
            RestResponse res = new RestResponse(false);
            res.setError(new RestError(401, "Access Denied [" + ex.getMessage() + "]"));
            httpResponse.setContentType(Constants.APPLICATION_JSON);
            httpResponse.getWriter().append(objectMapper.writeValueAsString(res));
            httpResponse.setStatus(401);
        } finally {
            MDC.clear();
        }
    }

    private boolean applyRateLimit(String header, HttpServletRequest httpRequest) throws URISyntaxException {
        if (!rateLimitEnabled) {
            return true;
        }
        if (null == header)
            header = getUrlKey(httpRequest.getRequestURI());
        Bucket bucket = buckets.computeIfAbsent(header, key -> createNewBucket());
        ConsumptionProbe probe = bucket.tryConsumeAndReturnRemaining(1);
        if (probe.isConsumed())
            return true;
        else
            throw new RateLimitExceededException("Too many requests");
    }

    private String getUrlKey(String url) throws URISyntaxException {
        URI uri = new URI(url);
        String path = uri.getPath();
        // Remove slashes and split the path into segments
        String[] pathSegments = path.split("/");

        // Construct the new format
        StringBuilder newFormat = new StringBuilder();
        for (String segment : pathSegments) {
            if (!segment.isEmpty()) {
                newFormat.append(segment).append("-");
            }
        }
        newFormat.append("key");
        return newFormat.toString();
    }

    private Bucket createNewBucket() {
        Bandwidth limit = Bandwidth.simple(rateLimitRequestLimit, Duration.ofSeconds(rateLimitRequestTime));
        return Bucket.builder()
                .addLimit(limit)
                .build();
    }

}
