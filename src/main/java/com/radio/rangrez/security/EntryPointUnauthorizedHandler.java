package com.radio.rangrez.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.radio.rangrez.model.response.RestError;
import com.radio.rangrez.model.response.RestResponse;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class EntryPointUnauthorizedHandler implements AuthenticationEntryPoint {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
        RestResponse res = new RestResponse(false);
        res.setError(new RestError(401, authException.getMessage()));

        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.getWriter().append(objectMapper.writeValueAsString(res));
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setStatus(401);
    }

}
