package com.radio.rangrez.service.album;

import com.radio.rangrez.dto.AlbumDto;
import com.radio.rangrez.dto.CreateAlbumRequest;
import com.radio.rangrez.dto.SongDto;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface AlbumService {

    AlbumDto createAlbum(CreateAlbumRequest request);

    List<AlbumDto> getAllAlbums();

    List<AlbumDto> getActiveAlbums();

    AlbumDto getAlbumById(Long id);

    AlbumDto updateAlbum(Long id, CreateAlbumRequest request);

    boolean deleteAlbum(Long id);

    List<SongDto> getAlbumSongs(Long albumId);

    AlbumDto addSongToAlbum(Long albumId, Long songId, Integer trackNumber);

    AlbumDto removeSongFromAlbum(Long albumId, Long songId);

    List<AlbumDto> getAlbumsByArtist(Long artistId);

    List<SongDto> getAvailableSongsForAlbum(Long albumId);
}
