package com.radio.rangrez.service.auth;

import com.radio.rangrez.dto.auth.AdminLoginRequest;
import com.radio.rangrez.dto.auth.AuthAdminResponse;
import com.radio.rangrez.exception.BadRequestException;
import com.radio.rangrez.exception.NotAcceptableException;
import com.radio.rangrez.exception.NotFoundException;
import com.radio.rangrez.model.Admin;
import com.radio.rangrez.repository.AdminRepository;
import com.radio.rangrez.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AdminService {

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private JwtUtil jwtUtil;

    public AuthAdminResponse login(AdminLoginRequest request) {
        Admin admin = adminRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new NotFoundException(NotFoundException.NotFoundType.ADMIN_NOT_FOUND));

        if (!request.getPassword().equals(admin.getPassword())) {
            throw new NotAcceptableException(NotAcceptableException.NotAcceptableExceptionMSG.PASSWORD_DOES_NOT_MATCH);
        }

        String token = jwtUtil.generateToken(admin.getEmail());

        return new AuthAdminResponse(token, admin.getEmail());
    }
}
