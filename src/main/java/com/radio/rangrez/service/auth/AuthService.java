package com.radio.rangrez.service.auth;

import com.radio.rangrez.dto.UserDto;
import com.radio.rangrez.dto.auth.AuthResponse;
import com.radio.rangrez.dto.auth.CompleteProfileRequest;
import com.radio.rangrez.dto.auth.SendOtpRequest;
import com.radio.rangrez.dto.auth.VerifyOtpRequest;
import com.radio.rangrez.exception.ServiceException;
import com.radio.rangrez.exception.ValidationException;
import com.radio.rangrez.service.user.UserService;
import com.radio.rangrez.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AuthService {

    @Autowired
    private CognitoService cognitoService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    public boolean sendOtp(SendOtpRequest request) {
        return cognitoService.sendOtp(request.getEmail(), "User@123");
    }

    public AuthResponse verifyOtp(VerifyOtpRequest request) {
        try {
            boolean isOtpValid = cognitoService.verifyOtp(request.getEmail(), request.getOtp());

            if (!isOtpValid) {
                throw new ValidationException(ValidationException.ValidationExceptionType.INVALID_TOKEN);
            }

            String email = request.getEmail();
            boolean isNewUser = userService.isNewUser(email);

            UserDto user;
            if (isNewUser) {
                // Create new user in our database
                String cognitoUserId = cognitoService.getCognitoUserId(email);
                user = userService.createUser(email, cognitoUserId);
            } else {
                // Get existing user
                user = userService.findByEmail(email);
            }

            // Generate JWT token
            String token = jwtUtil.generateToken(email);

            return new AuthResponse(
                token,
                email,
                isNewUser,
                user.isProfileCompleted(),
                user.getOnboardingCompleted()
            );

        } catch (Exception e) {
            throw new ServiceException(ServiceException.ServiceExceptionType.COGNITO_VERIFY_OTP_ERROR);
        }
    }

    public UserDto completeProfile(String email, CompleteProfileRequest request) {
        try {
            return userService.completeProfile(email, request);
        } catch (Exception e) {
            throw new ServiceException(ServiceException.ServiceExceptionType.COGNITO_SERVICE_ERROR);
        }
    }

    public UserDto updateProfile(String email, CompleteProfileRequest request) {
        try {
            return userService.updateProfile(email, request);
        } catch (Exception e) {
            throw new ServiceException(ServiceException.ServiceExceptionType.COGNITO_SERVICE_ERROR);
        }
    }

    public UserDto getUserProfile(String email) {
        try {
            return userService.getUserProfile(email);
        } catch (Exception e) {
            throw new ServiceException(ServiceException.ServiceExceptionType.COGNITO_SERVICE_ERROR);
        }
    }
}
