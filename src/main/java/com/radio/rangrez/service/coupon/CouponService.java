package com.radio.rangrez.service.coupon;

import com.radio.rangrez.dto.CreateCouponRequest;
import com.radio.rangrez.dto.CouponDto;
import com.radio.rangrez.dto.UpdateCouponPriorityRequest;
import com.radio.rangrez.model.Coupon;

import java.util.List;

public interface CouponService {
    
    CouponDto createCoupon(CreateCouponRequest request);
    
    List<CouponDto> getAllCoupons();
    
    List<CouponDto> getActiveCoupons();
    
    List<CouponDto> getActiveAndValidCoupons();
    
    List<CouponDto> getExpiredCoupons();
    
    CouponDto getCouponById(Long id);
    
    CouponDto getCouponByCode(String couponCode);
    
    CouponDto updateCoupon(Long id, CreateCouponRequest request);

    boolean deleteCoupon(Long id);

    int expireAndDeactivateCoupons();

    List<CouponDto> getCouponsByType(Coupon.CouponType type);

    List<CouponDto> getActiveAndValidCouponsByType(Coupon.CouponType type);
}
