package com.radio.rangrez.service.coupon.impl;

import com.radio.rangrez.dto.CreateCouponRequest;
import com.radio.rangrez.dto.CouponDto;
import com.radio.rangrez.dto.CouponOrderDTO;
import com.radio.rangrez.dto.UpdateCouponPriorityRequest;
import com.radio.rangrez.exception.ConflictException;
import com.radio.rangrez.exception.NotFoundException;
import com.radio.rangrez.exception.ValidationException;
import com.radio.rangrez.model.Coupon;
import com.radio.rangrez.repository.CouponRepository;
import com.radio.rangrez.service.coupon.CouponService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CouponServiceImpl implements CouponService {

    @Autowired
    private CouponRepository couponRepository;

    @Override
    public CouponDto createCoupon(CreateCouponRequest request) {
        // Check if coupon code already exists
        if (couponRepository.existsByCouponCodeIgnoreCase(request.getCouponCode())) {
            throw new ConflictException(ConflictException.ConflictExceptionType.COUPON_CODE_ALREADY_EXISTS);
        }

        // Validate expire time is in the future
        if (request.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new ValidationException(ValidationException.ValidationExceptionType.INVALID_EXPIRE_TIME);
        }

        Coupon coupon = new Coupon();
        BeanUtils.copyProperties(request, coupon, "id", "created", "updated");
        
        Coupon savedCoupon = couponRepository.save(coupon);
        return convertToDto(savedCoupon);
    }

    @Override
    public List<CouponDto> getAllCoupons() {
        List<Coupon> coupons = couponRepository.findAll();
        return coupons.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CouponDto> getActiveCoupons() {
        List<Coupon> coupons = couponRepository.findByActivatedTrueOrderByPriorityAscNameAsc();
        return coupons.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CouponDto> getActiveAndValidCoupons() {
        List<Coupon> coupons = couponRepository.findActiveAndNotExpiredCoupons(LocalDateTime.now());
        return coupons.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CouponDto> getExpiredCoupons() {
        List<Coupon> coupons = couponRepository.findActiveAndExpiredCoupons(LocalDateTime.now());
        return coupons.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CouponDto getCouponById(Long id) {
        Optional<Coupon> couponOptional = couponRepository.findById(id);
        if (couponOptional.isPresent()) {
            return convertToDto(couponOptional.get());
        }
        throw new NotFoundException(NotFoundException.NotFoundType.COUPON_NOT_FOUND);
    }

    @Override
    public CouponDto getCouponByCode(String couponCode) {
        Optional<Coupon> couponOptional = couponRepository.findByCouponCodeIgnoreCase(couponCode);
        if (couponOptional.isPresent()) {
            return convertToDto(couponOptional.get());
        }
        throw new NotFoundException(NotFoundException.NotFoundType.COUPON_CODE_NOT_FOUND);
    }

    @Override
    public CouponDto updateCoupon(Long id, CreateCouponRequest request) {
        Optional<Coupon> couponOptional = couponRepository.findById(id);
        if (couponOptional.isPresent()) {
            Coupon existingCoupon = couponOptional.get();
            
            // Check if coupon code is being changed and if new code already exists
            if (!existingCoupon.getCouponCode().equalsIgnoreCase(request.getCouponCode()) &&
                couponRepository.existsByCouponCodeIgnoreCase(request.getCouponCode())) {
                throw new ConflictException(ConflictException.ConflictExceptionType.COUPON_CODE_ALREADY_EXISTS);
            }

            // Validate expire time is in the future (only for new expire times)
            if (request.getExpireTime() != null && request.getExpireTime().isBefore(LocalDateTime.now())) {
                throw new ValidationException(ValidationException.ValidationExceptionType.INVALID_EXPIRE_TIME);
            }
            
            // Perform partial update
            copyNonNullProperties(request, existingCoupon);
            
            Coupon updatedCoupon = couponRepository.save(existingCoupon);
            return convertToDto(updatedCoupon);
        }
        throw new NotFoundException(NotFoundException.NotFoundType.COUPON_NOT_FOUND);
    }

    @Override
    public boolean deleteCoupon(Long id) {
        Optional<Coupon> couponOptional = couponRepository.findById(id);
        if (couponOptional.isPresent()) {
            couponRepository.deleteById(id);
            return true;
        }
        throw new NotFoundException(NotFoundException.NotFoundType.COUPON_NOT_FOUND);
    }

    @Override
    @Transactional
    public int expireAndDeactivateCoupons() {
        List<Coupon> expiredCoupons = couponRepository.findActiveAndExpiredCoupons(LocalDateTime.now());

        if (expiredCoupons.isEmpty()) {
            return 0;
        }

        int deactivatedCount = 0;
        for (Coupon coupon : expiredCoupons) {
            coupon.setActivated(false);
            couponRepository.save(coupon);
            deactivatedCount++;
        }

        return deactivatedCount;
    }

    @Override
    public List<CouponDto> getCouponsByType(Coupon.CouponType type) {
        List<Coupon> coupons = couponRepository.findByActivatedTrueAndTypeOrderByPriorityAscNameAsc(type);
        return coupons.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CouponDto> getActiveAndValidCouponsByType(Coupon.CouponType type) {
        List<Coupon> coupons = couponRepository.findActiveAndNotExpiredCouponsByType(type, LocalDateTime.now());
        return coupons.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void reorderCoupons(List<CouponOrderDTO> orderList) {
        for (CouponOrderDTO dto : orderList) {
            couponRepository.findById(dto.getId())
                    .ifPresent(coupon -> {
                        coupon.setPriority(dto.getPriority());
                        couponRepository.save(coupon);
                    });
        }
    }

    private CouponDto convertToDto(Coupon coupon) {
        CouponDto dto = new CouponDto();
        BeanUtils.copyProperties(coupon, dto);
        
        // Set expired flag
        dto.setExpired(coupon.getExpireTime().isBefore(LocalDateTime.now()));
        
        return dto;
    }

    private void copyNonNullProperties(CreateCouponRequest source, Coupon target) {
        if (source.getName() != null) {
            target.setName(source.getName());
        }
        if (source.getImage() != null) {
            target.setImage(source.getImage());
        }
        if (source.getDescription() != null) {
            target.setDescription(source.getDescription());
        }
        if (source.getShortDescription() != null) {
            target.setShortDescription(source.getShortDescription());
        }
        if (source.getCouponCode() != null) {
            target.setCouponCode(source.getCouponCode());
        }
        if (source.getExpireTime() != null) {
            target.setExpireTime(source.getExpireTime());
        }
        if (source.getPriority() != null) {
            target.setPriority(source.getPriority());
        }
        if (source.getType() != null) {
            target.setType(source.getType());
        }
    }
}
