package com.radio.rangrez.service.event;

import com.radio.rangrez.dto.event.CreateEventRequest;
import com.radio.rangrez.dto.event.EventDto;
import com.radio.rangrez.dto.event.EventResponse;
import com.radio.rangrez.dto.event.UpdateEventRequest;

import java.time.LocalDateTime;
import java.util.List;

public interface EventService {
    
    EventResponse createEvent(CreateEventRequest request, String userEmail);
    
    EventResponse updateEvent(Long eventId, UpdateEventRequest request, String userEmail);
    
    boolean deleteEvent(Long eventId, String userEmail);
    
    EventDto getEventById(Long eventId);
    
    List<EventDto> getAllEvents();
    
    List<EventDto> getEventsByUser(String userEmail);
    
    List<EventDto> getUpcomingEvents();
    
    List<EventDto> getPastEvents();
    
    List<EventDto> getEventsBetweenDates(LocalDateTime startDate, LocalDateTime endDate);
    
    EventResponse syncEventWithGoogleCalendar(Long eventId, String userEmail);
}
