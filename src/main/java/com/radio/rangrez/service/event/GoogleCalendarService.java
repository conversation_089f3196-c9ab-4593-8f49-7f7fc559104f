package com.radio.rangrez.service.event;

import com.google.api.client.util.DateTime;
import com.google.api.services.calendar.Calendar;
import com.google.api.services.calendar.model.Event;
import com.google.api.services.calendar.model.EventAttendee;
import com.google.api.services.calendar.model.EventDateTime;
import com.radio.rangrez.dto.event.CreateEventRequest;
import com.radio.rangrez.dto.event.UpdateEventRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class GoogleCalendarService {

    @Autowired(required = false)
    private Calendar googleCalendar;

    @Value("${google.calendar.enabled:false}")
    private boolean googleCalendarEnabled;

    /**
     * Creates a new event in Google Calendar
     */
    public Event createCalendarEvent(CreateEventRequest request, String userEmail) throws IOException {
        if (!googleCalendarEnabled || googleCalendar == null) {
            throw new IOException("Google Calendar integration is not enabled or configured");
        }
        Event event = new Event()
                .setSummary(request.getTitle())
                .setDescription(buildEventDescription(request.getDescription(), request.getLiveRadioUrl()));

        // Set start time
        ZonedDateTime startZoned = request.getStartDateTime().atZone(ZoneId.of(request.getTimezone()));
        DateTime startDateTime = new DateTime(startZoned.toInstant().toEpochMilli());
        EventDateTime start = new EventDateTime()
                .setDateTime(startDateTime)
                .setTimeZone(request.getTimezone());
        event.setStart(start);

        // Set end time
        ZonedDateTime endZoned = request.getEndDateTime().atZone(ZoneId.of(request.getTimezone()));
        DateTime endDateTime = new DateTime(endZoned.toInstant().toEpochMilli());
        EventDateTime end = new EventDateTime()
                .setDateTime(endDateTime)
                .setTimeZone(request.getTimezone());
        event.setEnd(end);

        // Set location
        if (request.getLocation() != null && !request.getLocation().isEmpty()) {
            event.setLocation(request.getLocation());
        }

        // Set attendees
        if (request.getAttendees() != null && !request.getAttendees().isEmpty()) {
            List<EventAttendee> attendees = new ArrayList<>();
            for (String email : request.getAttendees()) {
                attendees.add(new EventAttendee().setEmail(email));
            }
            event.setAttendees(attendees);
        }

        // Set event status
        event.setStatus(request.getEventStatus().name().toLowerCase());

        String calendarId = request.getCalendarId() != null ? request.getCalendarId() : "primary";
        
        try {
            Event createdEvent = googleCalendar.events().insert(calendarId, event).execute();
            log.info("Event created successfully: {}", createdEvent.getId());
            return createdEvent;
        } catch (IOException e) {
            log.error("Error creating Google Calendar event: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Updates an existing event in Google Calendar
     */
    public Event updateCalendarEvent(String eventId, UpdateEventRequest request, String calendarId) throws IOException {
        if (!googleCalendarEnabled || googleCalendar == null) {
            throw new IOException("Google Calendar integration is not enabled or configured");
        }

        try {
            // Get the existing event
            Event existingEvent = googleCalendar.events().get(calendarId, eventId).execute();

            // Update fields if provided
            if (request.getTitle() != null) {
                existingEvent.setSummary(request.getTitle());
            }

            if (request.getDescription() != null || request.getLiveRadioUrl() != null) {
                String description = request.getDescription() != null ? request.getDescription() : 
                    (existingEvent.getDescription() != null ? existingEvent.getDescription() : "");
                String liveRadioUrl = request.getLiveRadioUrl();
                existingEvent.setDescription(buildEventDescription(description, liveRadioUrl));
            }

            if (request.getStartDateTime() != null) {
                String timezone = request.getTimezone() != null ? request.getTimezone() : "UTC";
                ZonedDateTime startZoned = request.getStartDateTime().atZone(ZoneId.of(timezone));
                DateTime startDateTime = new DateTime(startZoned.toInstant().toEpochMilli());
                EventDateTime start = new EventDateTime()
                        .setDateTime(startDateTime)
                        .setTimeZone(timezone);
                existingEvent.setStart(start);
            }

            if (request.getEndDateTime() != null) {
                String timezone = request.getTimezone() != null ? request.getTimezone() : "UTC";
                ZonedDateTime endZoned = request.getEndDateTime().atZone(ZoneId.of(timezone));
                DateTime endDateTime = new DateTime(endZoned.toInstant().toEpochMilli());
                EventDateTime end = new EventDateTime()
                        .setDateTime(endDateTime)
                        .setTimeZone(timezone);
                existingEvent.setEnd(end);
            }

            if (request.getLocation() != null) {
                existingEvent.setLocation(request.getLocation());
            }

            if (request.getAttendees() != null) {
                List<EventAttendee> attendees = new ArrayList<>();
                for (String email : request.getAttendees()) {
                    attendees.add(new EventAttendee().setEmail(email));
                }
                existingEvent.setAttendees(attendees);
            }

            if (request.getEventStatus() != null) {
                existingEvent.setStatus(request.getEventStatus().name().toLowerCase());
            }

            Event updatedEvent = googleCalendar.events().update(calendarId, eventId, existingEvent).execute();
            log.info("Event updated successfully: {}", updatedEvent.getId());
            return updatedEvent;

        } catch (IOException e) {
            log.error("Error updating Google Calendar event: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Deletes an event from Google Calendar
     */
    public void deleteCalendarEvent(String eventId, String calendarId) throws IOException {
        if (!googleCalendarEnabled || googleCalendar == null) {
            throw new IOException("Google Calendar integration is not enabled or configured");
        }

        try {
            googleCalendar.events().delete(calendarId, eventId).execute();
            log.info("Event deleted successfully: {}", eventId);
        } catch (IOException e) {
            log.error("Error deleting Google Calendar event: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Gets an event from Google Calendar
     */
    public Event getCalendarEvent(String eventId, String calendarId) throws IOException {
        if (!googleCalendarEnabled || googleCalendar == null) {
            throw new IOException("Google Calendar integration is not enabled or configured");
        }

        try {
            return googleCalendar.events().get(calendarId, eventId).execute();
        } catch (IOException e) {
            log.error("Error getting Google Calendar event: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Check if Google Calendar integration is available
     */
    public boolean isGoogleCalendarEnabled() {
        return googleCalendarEnabled && googleCalendar != null;
    }

    /**
     * Builds event description with clickable live radio URL
     */
    private String buildEventDescription(String description, String liveRadioUrl) {
        StringBuilder descBuilder = new StringBuilder();
        
        if (description != null && !description.isEmpty()) {
            descBuilder.append(description);
        }
        
        if (liveRadioUrl != null && !liveRadioUrl.isEmpty()) {
            if (descBuilder.length() > 0) {
                descBuilder.append("\n\n");
            }
            descBuilder.append("🎵 Live Radio Stream: ");
            descBuilder.append(liveRadioUrl);
            descBuilder.append("\n\nClick the link above to join the live radio stream!");
        }
        
        return descBuilder.toString();
    }
}
