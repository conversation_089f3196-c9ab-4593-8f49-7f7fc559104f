package com.radio.rangrez.service.genre;

import com.radio.rangrez.dto.CreateGenreRequest;
import com.radio.rangrez.dto.GenreDto;

import java.util.List;

public interface GenreService {
    
    GenreDto createGenre(CreateGenreRequest request);
    
    List<GenreDto> getAllGenres();
    
    List<GenreDto> getActiveGenres();
    
    GenreDto getGenreById(Long id);
    
    GenreDto updateGenre(Long id, CreateGenreRequest request);
    
    boolean deleteGenre(Long id);
}
