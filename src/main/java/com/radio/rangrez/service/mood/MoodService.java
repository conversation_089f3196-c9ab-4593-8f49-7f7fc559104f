package com.radio.rangrez.service.mood;

import com.radio.rangrez.dto.CreateMoodRequest;
import com.radio.rangrez.dto.MoodDto;

import java.util.List;

public interface MoodService {
    
    MoodDto createMood(CreateMoodRequest request);
    
    List<MoodDto> getAllMoods();
    
    List<MoodDto> getActiveMoods();
    
    MoodDto getMoodById(Long id);
    
    MoodDto updateMood(Long id, CreateMoodRequest request);
    
    boolean deleteMood(Long id);
}
