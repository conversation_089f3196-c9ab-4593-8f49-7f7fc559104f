package com.radio.rangrez.service.mood.impl;

import com.radio.rangrez.dto.CreateMoodRequest;
import com.radio.rangrez.dto.MoodDto;
import com.radio.rangrez.exception.ConflictException;
import com.radio.rangrez.exception.NotFoundException;
import com.radio.rangrez.model.Mood;
import com.radio.rangrez.repository.MoodRepository;
import com.radio.rangrez.service.mood.MoodService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MoodServiceImpl implements MoodService {

    @Autowired
    private MoodRepository moodRepository;

    @Override
    public MoodDto createMood(CreateMoodRequest request) {
        // Check if mood already exists
        if (moodRepository.existsByNameIgnoreCase(request.getName())) {
            throw new ConflictException(ConflictException.ConflictExceptionType.MOOD_ALREADY_EXISTS);
        }

        Mood mood = new Mood();
        BeanUtils.copyProperties(request, mood, "id", "created", "updated");
        
        Mood savedMood = moodRepository.save(mood);
        return convertToDto(savedMood);
    }

    @Override
    public List<MoodDto> getAllMoods() {
        List<Mood> moods = moodRepository.findAll();
        return moods.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<MoodDto> getActiveMoods() {
        List<Mood> moods = moodRepository.findByActivatedTrueOrderByDisplayOrderAscNameAsc();
        return moods.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public MoodDto getMoodById(Long id) {
        Optional<Mood> moodOptional = moodRepository.findById(id);
        if (moodOptional.isPresent()) {
            return convertToDto(moodOptional.get());
        }
        throw new NotFoundException(NotFoundException.NotFoundType.MOOD_NOT_FOUND);
    }

    @Override
    public MoodDto updateMood(Long id, CreateMoodRequest request) {
        Optional<Mood> moodOptional = moodRepository.findById(id);
        if (moodOptional.isPresent()) {
            Mood existingMood = moodOptional.get();
            
            // Check if name is being changed and if new name already exists
            if (!existingMood.getName().equalsIgnoreCase(request.getName()) &&
                moodRepository.existsByNameIgnoreCase(request.getName())) {
                throw new ConflictException(ConflictException.ConflictExceptionType.MOOD_ALREADY_EXISTS);
            }
            
            // Perform partial update
            copyNonNullProperties(request, existingMood);
            
            Mood updatedMood = moodRepository.save(existingMood);
            return convertToDto(updatedMood);
        }
        throw new NotFoundException(NotFoundException.NotFoundType.MOOD_NOT_FOUND);
    }

    @Override
    public boolean deleteMood(Long id) {
        Optional<Mood> moodOptional = moodRepository.findById(id);
        if (moodOptional.isPresent()) {
            moodRepository.deleteById(id);
            return true;
        }
        throw new NotFoundException(NotFoundException.NotFoundType.MOOD_NOT_FOUND);
    }

    private MoodDto convertToDto(Mood mood) {
        MoodDto dto = new MoodDto();
        BeanUtils.copyProperties(mood, dto);
        return dto;
    }

    private void copyNonNullProperties(CreateMoodRequest source, Mood target) {
        if (source.getName() != null) {
            target.setName(source.getName());
        }
        if (source.getDescription() != null) {
            target.setDescription(source.getDescription());
        }
        if (source.getIconUrl() != null) {
            target.setIconUrl(source.getIconUrl());
        }
        if (source.getColorCode() != null) {
            target.setColorCode(source.getColorCode());
        }
        if (source.getDisplayOrder() != null) {
            target.setDisplayOrder(source.getDisplayOrder());
        }
    }
}
