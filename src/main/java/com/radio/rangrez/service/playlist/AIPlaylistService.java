package com.radio.rangrez.service.playlist;

import com.radio.rangrez.dto.playlist.PlaylistMetadata;
import com.radio.rangrez.dto.playlist.PlaylistQueryRequest;
import com.radio.rangrez.dto.playlist.PlaylistResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AIPlaylistService {

    @Autowired
    private OpenAIPlaylistService openAIPlaylistService;

    @Autowired
    private PlaylistGenerationService playlistGenerationService;

    /**
     * Main method that takes a user query and returns a generated playlist
     * 
     * @param request The user query request
     * @return PlaylistResponse with generated playlist
     */
    public PlaylistResponse generatePlaylistFromQuery(PlaylistQueryRequest request) {
        try {
            log.info("Processing playlist request for query: {}", request.getUserQuery());

            // Step 1: Extract metadata from user query using OpenAI
            PlaylistMetadata metadata = openAIPlaylistService.extractMetadataFromQuery(request.getUserQuery());
            log.info("Extracted metadata: {}", metadata);

            // Step 2: Generate playlist based on metadata
            PlaylistResponse response = playlistGenerationService.generatePlaylist(metadata);
            log.info("Generated playlist with {} tracks", response.getTracks().size());

            return response;

        } catch (Exception e) {
            log.error("Error generating playlist from query: {}", e.getMessage(), e);
            
            // Return empty playlist on error
            PlaylistResponse errorResponse = new PlaylistResponse();
            errorResponse.setPlaylist_name("Error generating playlist");
            errorResponse.setTracks(java.util.Collections.emptyList());
            return errorResponse;
        }
    }
}
