package com.radio.rangrez.service.playlist;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.radio.rangrez.dto.playlist.PlaylistMetadata;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.ChatMessageRole;
import com.theokanning.openai.service.OpenAiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class OpenAIPlaylistService {

    @Autowired
    private OpenAiService openAiService;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${openai.model:gpt-3.5-turbo}")
    private String model;

    @Value("${openai.max-tokens:500}")
    private Integer maxTokens;

    @Value("${openai.temperature:0.3}")
    private Double temperature;

    public PlaylistMetadata extractMetadataFromQuery(String userQuery) {
        try {
            String systemPrompt = createSystemPrompt();
            String userPrompt = "User Query: \"" + userQuery + "\"";

            List<ChatMessage> messages = Arrays.asList(
                new ChatMessage(ChatMessageRole.SYSTEM.value(), systemPrompt),
                new ChatMessage(ChatMessageRole.USER.value(), userPrompt)
            );

            ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .maxTokens(maxTokens)
                .temperature(temperature)
                .build();

            String response = openAiService.createChatCompletion(chatCompletionRequest)
                .getChoices().get(0).getMessage().getContent();

            log.info("OpenAI Response: {}", response);

            // Extract JSON from response (remove any markdown formatting)
            String jsonResponse = extractJsonFromResponse(response);
            
            return objectMapper.readValue(jsonResponse, PlaylistMetadata.class);
            
        } catch (Exception e) {
            log.error("Error extracting metadata from query: {}", e.getMessage());
            // Return default metadata with just the count
            PlaylistMetadata defaultMetadata = new PlaylistMetadata();
            defaultMetadata.setCount(10);
            return defaultMetadata;
        }
    }

    private String createSystemPrompt() {
        return """
            You are a music playlist metadata extractor. Extract the following fields from user queries and return ONLY a valid JSON object:
            
            Fields to extract:
            - genre (string): Main music genre
            - sub_genres (array): Sub-genres if mentioned
            - language (string): Language of songs
            - artist (array): Artist names mentioned
            - mood (string): Mood like happy, sad, romantic, etc.
            - activity (string): Activity like workout, study, party, etc.
            - era (string): Time period like 90s, 2000s, etc.
            - count (integer): Number of songs requested (default 10)
            - duration (integer): Duration in minutes if specified
            - tempo (string): Tempo like fast, slow, upbeat
            - explicit (boolean): If explicit content is requested
            - popularity (string): Like trending, latest, top hits
            - seed_tracks (array): Specific song names mentioned
            - seed_albums (array): Specific album names mentioned
            - region (string): Geographic region if mentioned
            
            Rules:
            1. Return ONLY valid JSON, no explanations
            2. Set fields to null if not mentioned in query
            3. Use lowercase for genre names
            4. Extract artist names exactly as mentioned
            5. Default count to 10 if not specified
            
            Example output:
            {
              "genre": "pop",
              "sub_genres": [],
              "language": "English",
              "artist": ["Arijit Singh"],
              "mood": null,
              "activity": "workout",
              "era": null,
              "count": 15,
              "duration": null,
              "tempo": null,
              "explicit": null,
              "popularity": null,
              "seed_tracks": [],
              "seed_albums": [],
              "region": null
            }
            """;
    }

    private String extractJsonFromResponse(String response) {
        // Remove markdown code blocks if present
        response = response.trim();
        if (response.startsWith("```json")) {
            response = response.substring(7);
        }
        if (response.startsWith("```")) {
            response = response.substring(3);
        }
        if (response.endsWith("```")) {
            response = response.substring(0, response.length() - 3);
        }
        return response.trim();
    }
}
