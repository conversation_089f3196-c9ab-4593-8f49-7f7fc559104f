package com.radio.rangrez.service.playlist;

import com.radio.rangrez.dto.SongDto;
import com.radio.rangrez.dto.playlist.PlaylistMetadata;
import com.radio.rangrez.dto.playlist.PlaylistResponse;
import com.radio.rangrez.model.*;
import com.radio.rangrez.repository.*;
import com.radio.rangrez.service.song.SongService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PlaylistGenerationService {

    @Autowired
    private SongRepository songRepository;

    @Autowired
    private SongGenreRepository songGenreRepository;

    @Autowired
    private SongArtistRepository songArtistRepository;

    @Autowired
    private SongMoodRepository songMoodRepository;

    @Autowired
    private GenreRepository genreRepository;

    @Autowired
    private ArtistRepository artistRepository;

    @Autowired
    private MoodRepository moodRepository;

    @Autowired
    private SongService songService;

    public PlaylistResponse generatePlaylist(PlaylistMetadata metadata) {
        try {
            log.info("Generating playlist with metadata: {}", metadata);

            // Get all songs that match the criteria
            Set<Song> candidateSongs = findMatchingSongs(metadata);
            
            // Convert to list and shuffle for randomness
            List<Song> songList = new ArrayList<>(candidateSongs);
            Collections.shuffle(songList);

            // Apply count limit
            int count = metadata.getCount() != null ? metadata.getCount() : 10;
            List<Song> selectedSongs = songList.stream()
                    .limit(count)
                    .collect(Collectors.toList());

            // Convert to response format
            PlaylistResponse response = new PlaylistResponse();
            response.setPlaylist_name(generatePlaylistName(metadata));
            response.setTracks(convertToPlaylistTracks(selectedSongs));

            log.info("Generated playlist with {} tracks", response.getTracks().size());
            return response;

        } catch (Exception e) {
            log.error("Error generating playlist: {}", e.getMessage());
            // Return empty playlist on error
            PlaylistResponse errorResponse = new PlaylistResponse();
            errorResponse.setPlaylist_name("No matching songs found");
            errorResponse.setTracks(new ArrayList<>());
            return errorResponse;
        }
    }

    private Set<Song> findMatchingSongs(PlaylistMetadata metadata) {
        Set<Song> allSongs = new HashSet<>();
        boolean hasFilters = false;

        // Filter by genre
        if (metadata.getGenre() != null && !metadata.getGenre().trim().isEmpty()) {
            Set<Song> genreSongs = findSongsByGenre(metadata.getGenre());
            if (hasFilters) {
                allSongs.retainAll(genreSongs);
            } else {
                allSongs.addAll(genreSongs);
                hasFilters = true;
            }
        }

        // Filter by artist
        if (metadata.getArtist() != null && !metadata.getArtist().isEmpty()) {
            Set<Song> artistSongs = findSongsByArtists(metadata.getArtist());
            if (hasFilters) {
                allSongs.retainAll(artistSongs);
            } else {
                allSongs.addAll(artistSongs);
                hasFilters = true;
            }
        }

        // Filter by language
        if (metadata.getLanguage() != null && !metadata.getLanguage().trim().isEmpty()) {
            Set<Song> languageSongs = findSongsByLanguage(metadata.getLanguage());
            if (hasFilters) {
                allSongs.retainAll(languageSongs);
            } else {
                allSongs.addAll(languageSongs);
                hasFilters = true;
            }
        }

        // Filter by mood
        if (metadata.getMood() != null && !metadata.getMood().trim().isEmpty()) {
            Set<Song> moodSongs = findSongsByMood(metadata.getMood());
            if (hasFilters) {
                allSongs.retainAll(moodSongs);
            } else {
                allSongs.addAll(moodSongs);
                hasFilters = true;
            }
        }

        // Filter by explicit content
        if (metadata.getExplicit() != null) {
            allSongs = allSongs.stream()
                    .filter(song -> song.isExplicit() == metadata.getExplicit())
                    .collect(Collectors.toSet());
        }

        // Filter by era (release year)
        if (metadata.getEra() != null && !metadata.getEra().trim().isEmpty()) {
            allSongs = filterByEra(allSongs, metadata.getEra());
        }

        // If no filters were applied, get all active songs
        if (!hasFilters) {
            allSongs.addAll(songRepository.findByActivatedTrueOrderByCreatedDesc());
        }

        // Apply popularity sorting if requested
        if (metadata.getPopularity() != null) {
            allSongs = applySorting(allSongs, metadata.getPopularity());
        }

        return allSongs;
    }

    private Set<Song> findSongsByGenre(String genreName) {
        Optional<Genre> genre = genreRepository.findByNameIgnoreCase(genreName);
        if (genre.isPresent()) {
            return new HashSet<>(songGenreRepository.findSongsByGenreId(genre.get().getId()));
        }
        return new HashSet<>();
    }

    private Set<Song> findSongsByArtists(List<String> artistNames) {
        Set<Song> songs = new HashSet<>();
        for (String artistName : artistNames) {
            // Find artists by name (you might need to add this method to ArtistRepository)
            List<Artist> artists = artistRepository.findAll().stream()
                    .filter(artist -> artist.getFullName().toLowerCase().contains(artistName.toLowerCase()) ||
                                    (artist.getStageName() != null && artist.getStageName().toLowerCase().contains(artistName.toLowerCase())))
                    .collect(Collectors.toList());
            
            for (Artist artist : artists) {
                songs.addAll(songArtistRepository.findSongsByArtistId(artist.getId()));
            }
        }
        return songs;
    }

    private Set<Song> findSongsByLanguage(String language) {
        return new HashSet<>(songRepository.findByLanguageContainingIgnoreCaseAndActivatedTrue(language));
    }

    private Set<Song> findSongsByMood(String moodName) {
        Optional<Mood> mood = moodRepository.findByNameIgnoreCase(moodName);
        if (mood.isPresent()) {
            return new HashSet<>(songMoodRepository.findSongsByMoodId(mood.get().getId()));
        }
        return new HashSet<>();
    }

    private Set<Song> filterByEra(Set<Song> songs, String era) {
        // Parse era like "90s", "2000s", etc.
        try {
            if (era.toLowerCase().contains("90s")) {
                return filterByYearRange(songs, 1990, 1999);
            } else if (era.toLowerCase().contains("2000s")) {
                return filterByYearRange(songs, 2000, 2009);
            } else if (era.toLowerCase().contains("2010s")) {
                return filterByYearRange(songs, 2010, 2019);
            } else if (era.toLowerCase().contains("2020s")) {
                return filterByYearRange(songs, 2020, 2029);
            }
        } catch (Exception e) {
            log.warn("Could not parse era: {}", era);
        }
        return songs;
    }

    private Set<Song> filterByYearRange(Set<Song> songs, int startYear, int endYear) {
        return songs.stream()
                .filter(song -> song.getReleaseDate() != null)
                .filter(song -> {
                    int year = song.getReleaseDate().getYear();
                    return year >= startYear && year <= endYear;
                })
                .collect(Collectors.toSet());
    }

    private Set<Song> applySorting(Set<Song> songs, String popularity) {
        List<Song> sortedSongs = new ArrayList<>(songs);
        
        switch (popularity.toLowerCase()) {
            case "trending":
            case "popular":
                sortedSongs.sort((a, b) -> Long.compare(b.getPlayCount(), a.getPlayCount()));
                break;
            case "latest":
                sortedSongs.sort((a, b) -> {
                    if (a.getReleaseDate() == null && b.getReleaseDate() == null) return 0;
                    if (a.getReleaseDate() == null) return 1;
                    if (b.getReleaseDate() == null) return -1;
                    return b.getReleaseDate().compareTo(a.getReleaseDate());
                });
                break;
            case "top hits":
                sortedSongs.sort((a, b) -> Double.compare(b.getAverageRating(), a.getAverageRating()));
                break;
        }
        
        return new LinkedHashSet<>(sortedSongs);
    }

    private String generatePlaylistName(PlaylistMetadata metadata) {
        StringBuilder name = new StringBuilder();
        
        if (metadata.getActivity() != null) {
            name.append(capitalize(metadata.getActivity())).append(" ");
        }
        
        if (metadata.getGenre() != null) {
            name.append(capitalize(metadata.getGenre())).append(" ");
        }
        
        if (metadata.getLanguage() != null) {
            name.append(metadata.getLanguage()).append(" ");
        }
        
        name.append("Hits");
        
        return name.toString().trim();
    }

    private String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }

    private List<PlaylistResponse.PlaylistTrack> convertToPlaylistTracks(List<Song> songs) {
        return songs.stream().map(song -> {
            SongDto songDto = songService.convertToDto(song);
            
            PlaylistResponse.PlaylistTrack track = new PlaylistResponse.PlaylistTrack();
            track.setTitle(song.getTitle());
            track.setLanguage(song.getLanguage());
            track.setDuration(song.getDurationSeconds());
            track.setFileUrl(song.getAudioFileUrl());
            
            // Get artist names
            if (songDto.getArtists() != null && !songDto.getArtists().isEmpty()) {
                track.setArtist(songDto.getArtists().get(0).getArtist().getFullName());
            }
            
            // Get genre names
            if (songDto.getGenres() != null && !songDto.getGenres().isEmpty()) {
                track.setGenre(songDto.getGenres().stream()
                        .map(genreDto -> genreDto.getName())
                        .collect(Collectors.toList()));
            }
            
            return track;
        }).collect(Collectors.toList());
    }
}
