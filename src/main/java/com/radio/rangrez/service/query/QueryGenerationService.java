package com.radio.rangrez.service.query;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class QueryGenerationService {

    public List<String> generateMusicQueries() {
        log.info("Generating static music query suggestions");
        
        return Arrays.asList(
            "I want to listen to <PERSON><PERSON> songs",
            "Play some happy songs",
            "Play Bollywood songs",
            "Play songs from the 90s"
        );
    }
}
