package com.radio.rangrez.service.question;

import com.radio.rangrez.dto.CreateQuestionRequest;
import com.radio.rangrez.dto.QuestionDto;
import com.radio.rangrez.dto.SubmitAllResponsesRequest;
import com.radio.rangrez.dto.SubmitQuestionResponseRequest;
import com.radio.rangrez.model.User;

import java.util.List;

public interface QuestionService {
    
    QuestionDto createQuestion(CreateQuestionRequest request);
    
    List<QuestionDto> getAllQuestions();
    
    List<QuestionDto> getActiveQuestions();
    
    QuestionDto getQuestionById(Long id);
    
    QuestionDto updateQuestion(Long id, CreateQuestionRequest request);

    boolean deleteQuestion(Long id);

    void submitResponse(SubmitQuestionResponseRequest request, User user);

    void submitAllResponses(SubmitAllResponsesRequest request, User user);
}
