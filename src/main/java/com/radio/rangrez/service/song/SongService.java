package com.radio.rangrez.service.song;

import com.radio.rangrez.dto.CreateSongRequest;
import com.radio.rangrez.dto.SongDto;
import com.radio.rangrez.dto.UpdateSongArtistsRequest;
import com.radio.rangrez.dto.UpdateSongGenresRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface SongService {
    
    SongDto createSong(CreateSongRequest request);
    
    List<SongDto> getAllSongs();
    
    Page<SongDto> getAllSongs(Pageable pageable);
    
    List<SongDto> getActiveSongs();
    
    SongDto getSongById(Long id);
    
    SongDto updateSong(Long id, CreateSongRequest request);

    SongDto updateSongGenres(Long id, UpdateSongGenresRequest request);

    SongDto updateSongArtists(Long id, UpdateSongArtistsRequest request);

    boolean deleteSong(Long id);
    
    List<SongDto> searchSongsByTitle(String title);
    
    List<SongDto> getSongsByAlbum(String albumName);
    
    List<SongDto> getSongsByLanguage(String language);
    
    List<SongDto> getFeaturedSongs();
    
    List<SongDto> getPopularSongs();
    
    List<SongDto> getTopRatedSongs();
    
    List<SongDto> getSongsByArtist(Long artistId);
    
    List<SongDto> getSongsByGenre(Long genreId);
    
    List<SongDto> getSongsByMood(Long moodId);
    
    SongDto incrementPlayCount(Long songId);
    
    SongDto rateSong(Long songId, Double rating);

    // Utility method for converting Song entity to DTO
    SongDto convertToDto(com.radio.rangrez.model.Song song);
}
