package com.radio.rangrez.service.song.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.radio.rangrez.dto.SongMetaData;
import com.radio.rangrez.dto.playlist.PlaylistQueryRequest;
import com.radio.rangrez.service.song.MetaDataService;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.ChatMessageRole;
import com.theokanning.openai.service.OpenAiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class MetaDataServiceImpl implements MetaDataService {

    @Autowired
    private OpenAiService openAiService;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${openai.model:gpt-3.5-turbo}")
    private String model;

    @Value("${openai.max-tokens:800}")
    private Integer maxTokens;

    @Value("${openai.temperature:0.3}")
    private Double temperature;

    @Override
    public SongMetaData extractMetadataFromQuery(PlaylistQueryRequest request) {
        try {
            log.info("Extracting song metadata from query: {}", request.getUserQuery());

            String systemPrompt = createSystemPrompt();
            String userPrompt = "Song Query: \"" + request.getUserQuery() + "\"";

            List<ChatMessage> messages = Arrays.asList(
                new ChatMessage(ChatMessageRole.SYSTEM.value(), systemPrompt),
                new ChatMessage(ChatMessageRole.USER.value(), userPrompt)
            );

            ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .maxTokens(maxTokens)
                .temperature(temperature)
                .build();

            String response = openAiService.createChatCompletion(chatCompletionRequest)
                .getChoices().get(0).getMessage().getContent();

            log.info("OpenAI Response for metadata extraction: {}", response);

            // Extract JSON from response (remove any markdown formatting)
            String jsonResponse = extractJsonFromResponse(response);

            SongMetaData metadata = objectMapper.readValue(jsonResponse, SongMetaData.class);
            log.info("Successfully extracted song metadata: {}", metadata);

            return metadata;

        } catch (Exception e) {
            log.error("Error extracting song metadata from query: {}", e.getMessage(), e);

            // Return basic metadata with song name if extraction fails
            SongMetaData defaultMetadata = new SongMetaData();
            defaultMetadata.setTitle(request.getUserQuery()); // Use query as fallback title
            return defaultMetadata;
        }
    }

    private String createSystemPrompt() {
        return """
            You are a music metadata extractor. Given a song name or query, extract comprehensive metadata and return ONLY a valid JSON object.

            Extract the following fields:
            - title (string): The exact song title
            - artist (string): Primary artist name
            - album (string): Album name if known
            - genre (string): Primary music genre
            - language (string): Song language (English, Hindi, Punjabi, etc.)
            - mood (string): Primary mood (happy, sad, romantic, energetic, etc.)
            - moods (array): All applicable moods
            - era (string): Time period (90s, 2000s, 2010s, 2020s, etc.)
            - spotifyArtistId (string): Primary Spotify artist ID
            - country (string): Country of origin
            - region (string): Geographic region=
            - tempo (string): Tempo description (slow, medium, fast, upbeat, etc.)
            - popularity (string): Popularity level (trending, popular, classic, etc.)
            - activity (string): Suitable activity (workout, study, party, relaxing, etc.)
            - coverImageUrl (string): Album/song cover image URL if known
            - audioPreviewUrl (string): Preview audio URL if known

            Rules:
            1. Return ONLY valid JSON, no explanations or markdown
            2. Set fields to null if not known or not applicable
            3. Use proper data types (strings, arrays, integers, booleans)
            4. For Spotify IDs, use actual IDs if you know them, otherwise set to null
            5. Be as comprehensive as possible with the information you know
            6. If the query is just a song name, extract all known metadata about that song

            Example output:
            {
              "title": "Shape of You",
              "artist": "Ed Sheeran",
              "album": "÷ (Divide)",
              "genre": "pop",
              "language": "English",
              "mood": "upbeat",
              "moods": ["upbeat", "romantic"],
              "era": "2010s",
              "spotifyArtistId": "6eUKZXaKkcviH0Ku9w2n3V",
              "country": "United Kingdom",
              "region": "Europe",
              "tempo": "upbeat",
              "popularity": "trending",
              "activity": "party",
              "coverImageUrl": null,
              "audioPreviewUrl": null
            }
            """;
    }

    private String extractJsonFromResponse(String response) {
        // Remove markdown code blocks if present
        String cleaned = response.trim();
        if (cleaned.startsWith("```json")) {
            cleaned = cleaned.substring(7);
        }
        if (cleaned.startsWith("```")) {
            cleaned = cleaned.substring(3);
        }
        if (cleaned.endsWith("```")) {
            cleaned = cleaned.substring(0, cleaned.length() - 3);
        }
        return cleaned.trim();
    }
}
