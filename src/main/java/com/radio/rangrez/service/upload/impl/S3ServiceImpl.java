package com.radio.rangrez.service.upload.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.radio.rangrez.config.AwsS3Config;
import com.radio.rangrez.enums.FileType;
import com.radio.rangrez.service.upload.S3Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
public class S3ServiceImpl implements S3Service {
    private final S3Client s3Client;

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    @Value("${aws.s3.region}")
    private String region;

    public S3ServiceImpl(S3Client s3Client) {
        this.s3Client = s3Client;
    }

    @Override
    public Map<String, String> uploadFile(MultipartFile file, FileType type) throws IOException {
        String folder = type.name().toLowerCase(); // audio or image
        String key = folder + "/" + UUID.randomUUID() + "_" + file.getOriginalFilename();

        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .contentType(file.getContentType())
                .build();
        try{
            s3Client.putObject(request, software.amazon.awssdk.core.sync.RequestBody.fromInputStream(
                    file.getInputStream(), file.getSize()
            ));
        } catch (Exception e){
            throw new IOException(e.getMessage());
        }


        String url = String.format("https://%s.s3.%s.amazonaws.com/%s",
                bucketName, region, key);

        Map<String, String> response = new HashMap<>();
        response.put("s3Key", key);
        response.put("url", url);
        return response;
    }
}
