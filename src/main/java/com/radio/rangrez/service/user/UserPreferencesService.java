package com.radio.rangrez.service.user;

import com.radio.rangrez.dto.UserPreferencesDto;
import com.radio.rangrez.dto.UserPreferencesRequest;

public interface UserPreferencesService {
    
    UserPreferencesDto getUserPreferences(String email);
    
    UserPreferencesDto updateUserPreferences(String email, UserPreferencesRequest request);
    
    UserPreferencesDto addGenrePreferences(String email, UserPreferencesRequest request);
    
    UserPreferencesDto addMoodPreferences(String email, UserPreferencesRequest request);
    
    boolean removeGenrePreference(String email, Long genreId);
    
    boolean removeMoodPreference(String email, Long moodId);
}
