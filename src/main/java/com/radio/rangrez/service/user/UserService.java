package com.radio.rangrez.service.user;

import com.radio.rangrez.dto.UserDto;
import com.radio.rangrez.dto.auth.CompleteProfileRequest;
import com.radio.rangrez.model.User;

public interface UserService {
    
    UserDto findByEmail(String email);
    
    UserDto createUser(String email, String cognitoUserId);
    
    UserDto completeProfile(String email, CompleteProfileRequest request);

    UserDto updateProfile(String email, CompleteProfileRequest request);
    
    boolean isNewUser(String email);
    
    UserDto getUserProfile(String email);

    User getCurrentUser();
}
