package com.radio.rangrez.service.user.impl;

import com.radio.rangrez.dto.GenreDto;
import com.radio.rangrez.dto.MoodDto;
import com.radio.rangrez.dto.UserPreferencesDto;
import com.radio.rangrez.dto.UserPreferencesRequest;
import com.radio.rangrez.exception.NotFoundException;
import com.radio.rangrez.model.*;
import com.radio.rangrez.repository.*;
import com.radio.rangrez.service.user.UserPreferencesService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class UserPreferencesServiceImpl implements UserPreferencesService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private GenreRepository genreRepository;

    @Autowired
    private MoodRepository moodRepository;

    @Autowired
    private UserGenrePreferenceRepository userGenrePreferenceRepository;

    @Autowired
    private UserMoodPreferenceRepository userMoodPreferenceRepository;

    @Override
    public UserPreferencesDto getUserPreferences(String email) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            
            List<Genre> genres = userGenrePreferenceRepository.findGenresByUserId(user.getId());
            List<Mood> moods = userMoodPreferenceRepository.findMoodsByUserId(user.getId());
            
            List<GenreDto> genreDtos = genres.stream()
                    .map(this::convertGenreToDto)
                    .collect(Collectors.toList());
            
            List<MoodDto> moodDtos = moods.stream()
                    .map(this::convertMoodToDto)
                    .collect(Collectors.toList());
            
            return new UserPreferencesDto(genreDtos, moodDtos);
        }
        throw new NotFoundException(NotFoundException.NotFoundType.USER_NOT_FOUND);
    }

    @Override
    @Transactional
    public UserPreferencesDto updateUserPreferences(String email, UserPreferencesRequest request) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            
            // Clear existing preferences
            userGenrePreferenceRepository.deleteByUserId(user.getId());
            userMoodPreferenceRepository.deleteByUserId(user.getId());
            
            // Add new genre preferences
            if (request.getGenreIds() != null) {
                for (Long genreId : request.getGenreIds()) {
                    Optional<Genre> genreOptional = genreRepository.findById(genreId);
                    if (genreOptional.isPresent()) {
                        UserGenrePreference preference = new UserGenrePreference();
                        preference.setUser(user);
                        preference.setGenre(genreOptional.get());
                        userGenrePreferenceRepository.save(preference);
                    }
                }
            }
            
            // Add new mood preferences
            if (request.getMoodIds() != null) {
                for (Long moodId : request.getMoodIds()) {
                    Optional<Mood> moodOptional = moodRepository.findById(moodId);
                    if (moodOptional.isPresent()) {
                        UserMoodPreference preference = new UserMoodPreference();
                        preference.setUser(user);
                        preference.setMood(moodOptional.get());
                        userMoodPreferenceRepository.save(preference);
                    }
                }
            }
            
            return getUserPreferences(email);
        }
        throw new RuntimeException("User not found with email: " + email);
    }

    @Override
    public UserPreferencesDto addGenrePreferences(String email, UserPreferencesRequest request) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            
            if (request.getGenreIds() != null) {
                for (Long genreId : request.getGenreIds()) {
                    Optional<Genre> genreOptional = genreRepository.findById(genreId);
                    if (genreOptional.isPresent() && !userGenrePreferenceRepository.existsByUserIdAndGenreId(user.getId(), genreId)) {
                        UserGenrePreference preference = new UserGenrePreference();
                        preference.setUser(user);
                        preference.setGenre(genreOptional.get());
                        userGenrePreferenceRepository.save(preference);
                    }
                }
            }
            
            return getUserPreferences(email);
        }
        throw new NotFoundException(NotFoundException.NotFoundType.USER_NOT_FOUND);
    }

    @Override
    public UserPreferencesDto addMoodPreferences(String email, UserPreferencesRequest request) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            
            if (request.getMoodIds() != null) {
                for (Long moodId : request.getMoodIds()) {
                    Optional<Mood> moodOptional = moodRepository.findById(moodId);
                    if (moodOptional.isPresent() && !userMoodPreferenceRepository.existsByUserIdAndMoodId(user.getId(), moodId)) {
                        UserMoodPreference preference = new UserMoodPreference();
                        preference.setUser(user);
                        preference.setMood(moodOptional.get());
                        userMoodPreferenceRepository.save(preference);
                    }
                }
            }
            
            return getUserPreferences(email);
        }
        throw new RuntimeException("User not found with email: " + email);
    }

    @Override
    public boolean removeGenrePreference(String email, Long genreId) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            userGenrePreferenceRepository.deleteByUserIdAndGenreId(user.getId(), genreId);
            return true;
        }
        throw new RuntimeException("User not found with email: " + email);
    }

    @Override
    public boolean removeMoodPreference(String email, Long moodId) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            userMoodPreferenceRepository.deleteByUserIdAndMoodId(user.getId(), moodId);
            return true;
        }
        throw new NotFoundException(NotFoundException.NotFoundType.USER_NOT_FOUND);
    }

    private GenreDto convertGenreToDto(Genre genre) {
        GenreDto dto = new GenreDto();
        BeanUtils.copyProperties(genre, dto);
        return dto;
    }

    private MoodDto convertMoodToDto(Mood mood) {
        MoodDto dto = new MoodDto();
        BeanUtils.copyProperties(mood, dto);
        return dto;
    }
}
